import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  AppBar,
  Too<PERSON>bar,
  IconButton,
  Card,
  CardContent,
  Grid,
  LinearProgress,
  Chip,
  <PERSON>ton,
  ButtonGroup,
  Avatar,
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import FavoriteIcon from '@mui/icons-material/Favorite';
import CreateIcon from '@mui/icons-material/Create';
import CenterFocusStrongIcon from '@mui/icons-material/CenterFocusStrong';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import { motion } from 'framer-motion';

const DataAnalysisPage = () => {
  const navigate = useNavigate();
  const [timeRange, setTimeRange] = useState('month'); // week, month, year

  const handleBack = () => {
    navigate('/app/profile');
  };

  // 模擬數據
  const analyticsData = {
    week: {
      totalEntries: 5,
      totalLikes: 23,
      totalComments: 8,
      focusSessions: 12,
      focusMinutes: 360,
      streak: 5,
      emotions: {
        gratitude: 40,
        joy: 25,
        peace: 15,
        love: 10,
        hope: 5,
        growth: 5,
      },
      dailyEntries: [1, 0, 1, 1, 1, 1, 0],
      weeklyTrend: '+20%',
    },
    month: {
      totalEntries: 18,
      totalLikes: 156,
      totalComments: 42,
      focusSessions: 45,
      focusMinutes: 1350,
      streak: 12,
      emotions: {
        gratitude: 35,
        joy: 20,
        peace: 18,
        love: 12,
        hope: 8,
        growth: 7,
      },
      dailyEntries: Array.from({length: 30}, () => Math.floor(Math.random() * 3)),
      weeklyTrend: '+15%',
    },
    year: {
      totalEntries: 156,
      totalLikes: 1240,
      totalComments: 328,
      focusSessions: 180,
      focusMinutes: 5400,
      streak: 45,
      emotions: {
        gratitude: 32,
        joy: 22,
        peace: 16,
        love: 14,
        hope: 9,
        growth: 7,
      },
      dailyEntries: Array.from({length: 365}, () => Math.floor(Math.random() * 4)),
      weeklyTrend: '+8%',
    },
  };

  const emotionColors = {
    gratitude: '#F59E0B',
    joy: '#FCD34D',
    peace: '#8B5CF6',
    love: '#C084FC',
    hope: '#10B981',
    growth: '#6B46C1',
  };

  const emotionLabels = {
    gratitude: '感恩',
    joy: '喜悅',
    peace: '平靜',
    love: '愛',
    hope: '希望',
    growth: '成長',
  };

  const currentData = analyticsData[timeRange];

  const StatCard = ({ icon, title, value, subtitle, trend, color = 'primary.main' }) => (
    <Card sx={{ borderRadius: 4, height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar sx={{ bgcolor: `${color}20`, color: color, mr: 2 }}>
            {icon}
          </Avatar>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            {title}
          </Typography>
        </Box>
        <Typography variant="h4" sx={{ fontWeight: 700, mb: 1, color: color }}>
          {value}
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="body2" color="text.secondary">
            {subtitle}
          </Typography>
          {trend && (
            <Chip
              label={trend}
              size="small"
              sx={{
                bgcolor: trend.startsWith('+') ? 'success.light' : 'error.light',
                color: trend.startsWith('+') ? 'success.main' : 'error.main',
                fontWeight: 600,
              }}
            />
          )}
        </Box>
      </CardContent>
    </Card>
  );

  const EmotionChart = () => (
    <Card sx={{ borderRadius: 4 }}>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
          情感分佈
        </Typography>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {Object.entries(currentData.emotions).map(([emotion, percentage]) => (
            <Box key={emotion}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  {emotionLabels[emotion]}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {percentage}%
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={percentage}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  bgcolor: 'grey.200',
                  '& .MuiLinearProgress-bar': {
                    bgcolor: emotionColors[emotion],
                    borderRadius: 4,
                  },
                }}
              />
            </Box>
          ))}
        </Box>
      </CardContent>
    </Card>
  );

  const ActivityHeatmap = () => {
    const getDayColor = (count) => {
      if (count === 0) return '#F3F4F6';
      if (count === 1) return '#A78BFA40';
      if (count === 2) return '#A78BFA80';
      return '#6B46C1';
    };

    return (
      <Card sx={{ borderRadius: 4 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
            活躍度熱力圖
          </Typography>
          <Box sx={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(7, 1fr)', 
            gap: 0.5,
            mb: 2,
          }}>
            {['日', '一', '二', '三', '四', '五', '六'].map((day) => (
              <Typography 
                key={day} 
                variant="caption" 
                sx={{ textAlign: 'center', color: 'text.secondary' }}
              >
                {day}
              </Typography>
            ))}
          </Box>
          <Box sx={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(7, 1fr)', 
            gap: 0.5,
            mb: 2,
          }}>
            {currentData.dailyEntries.slice(0, 35).map((count, index) => (
              <Box
                key={index}
                sx={{
                  width: 20,
                  height: 20,
                  borderRadius: 1,
                  bgcolor: getDayColor(count),
                  border: '1px solid',
                  borderColor: 'divider',
                }}
                title={`${count} 篇日記`}
              />
            ))}
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="caption" color="text.secondary">
              少
            </Typography>
            <Box sx={{ display: 'flex', gap: 0.5 }}>
              {[0, 1, 2, 3].map((level) => (
                <Box
                  key={level}
                  sx={{
                    width: 12,
                    height: 12,
                    borderRadius: 0.5,
                    bgcolor: getDayColor(level),
                    border: '1px solid',
                    borderColor: 'divider',
                  }}
                />
              ))}
            </Box>
            <Typography variant="caption" color="text.secondary">
              多
            </Typography>
          </Box>
        </CardContent>
      </Card>
    );
  };

  return (
    <Box sx={{ 
      bgcolor: 'background.default', 
      minHeight: '100vh',
      width: '100%',
      maxWidth: '100vw',
      margin: '0 auto',
    }}>
      {/* 頂部導航欄 */}
      <AppBar
        position="sticky"
        elevation={0}
        sx={{
          bgcolor: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(0,0,0,0.1)',
        }}
      >
        <Toolbar sx={{ maxWidth: '800px', width: '100%', margin: '0 auto' }}>
          <IconButton onClick={handleBack} sx={{ mr: 2 }}>
            <ArrowBackIcon sx={{ color: 'text.primary' }} />
          </IconButton>
          <Typography
            variant="h3"
            sx={{
              flex: 1,
              color: 'text.primary',
              fontWeight: 600,
            }}
          >
            數據分析
          </Typography>
        </Toolbar>
      </AppBar>

      <Box sx={{ 
        p: 2,
        maxWidth: '800px',
        width: '100%',
        margin: '0 auto',
      }}>
        {/* 時間範圍選擇 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Box sx={{ mb: 3, display: 'flex', justifyContent: 'center' }}>
            <ButtonGroup variant="outlined" sx={{ borderRadius: 3 }}>
              <Button
                variant={timeRange === 'week' ? 'contained' : 'outlined'}
                onClick={() => setTimeRange('week')}
              >
                本週
              </Button>
              <Button
                variant={timeRange === 'month' ? 'contained' : 'outlined'}
                onClick={() => setTimeRange('month')}
              >
                本月
              </Button>
              <Button
                variant={timeRange === 'year' ? 'contained' : 'outlined'}
                onClick={() => setTimeRange('year')}
              >
                本年
              </Button>
            </ButtonGroup>
          </Box>
        </motion.div>

        {/* 統計卡片 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={6} sm={3}>
              <StatCard
                icon={<CreateIcon />}
                title="記錄篇數"
                value={currentData.totalEntries}
                subtitle="總計"
                trend={currentData.weeklyTrend}
                color="primary.main"
              />
            </Grid>
            <Grid item xs={6} sm={3}>
              <StatCard
                icon={<FavoriteIcon />}
                title="獲得點讚"
                value={currentData.totalLikes}
                subtitle="社群互動"
                color="error.main"
              />
            </Grid>
            <Grid item xs={6} sm={3}>
              <StatCard
                icon={<CenterFocusStrongIcon />}
                title="專注次數"
                value={currentData.focusSessions}
                subtitle={`${currentData.focusMinutes} 分鐘`}
                color="success.main"
              />
            </Grid>
            <Grid item xs={6} sm={3}>
              <StatCard
                icon={<CalendarTodayIcon />}
                title="連續天數"
                value={currentData.streak}
                subtitle="記錄習慣"
                color="warning.main"
              />
            </Grid>
          </Grid>
        </motion.div>

        {/* 情感分佈圖表 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <EmotionChart />
            </Grid>
            <Grid item xs={12} md={6}>
              <ActivityHeatmap />
            </Grid>
          </Grid>
        </motion.div>

        {/* 成就和里程碑 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Card sx={{ borderRadius: 4 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                🏆 最近成就
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{ 
                    width: 40, 
                    height: 40, 
                    borderRadius: '50%', 
                    bgcolor: 'success.light',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '1.2rem',
                  }}>
                    🔥
                  </Box>
                  <Box>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      連續記錄 {currentData.streak} 天
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      保持良好的記錄習慣
                    </Typography>
                  </Box>
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{ 
                    width: 40, 
                    height: 40, 
                    borderRadius: '50%', 
                    bgcolor: 'primary.light',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '1.2rem',
                  }}>
                    ⭐
                  </Box>
                  <Box>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      社群之星
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      獲得 {currentData.totalLikes} 個點讚
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{ 
                    width: 40, 
                    height: 40, 
                    borderRadius: '50%', 
                    bgcolor: 'warning.light',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '1.2rem',
                  }}>
                    🎯
                  </Box>
                  <Box>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      專注達人
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      完成 {currentData.focusSessions} 次專注工作
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </motion.div>
      </Box>
    </Box>
  );
};

export default DataAnalysisPage;
