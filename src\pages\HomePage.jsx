import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  AppB<PERSON>,
  Toolbar,
  IconButton,
  Chip,
  Card,
  CardContent,
  Avatar,
  Button,
  Grid,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import FavoriteIcon from '@mui/icons-material/Favorite';
import ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';
import ShareIcon from '@mui/icons-material/Share';
import ImageIcon from '@mui/icons-material/Image';
import MicIcon from '@mui/icons-material/Mic';
import { motion } from 'framer-motion';

// 模擬數據
const emotionFilters = [
  { id: 'all', label: '全部', emoji: '✨', color: '#6B7280' },
  { id: 'gratitude', label: '感恩', emoji: '🙏', color: '#F59E0B' },
  { id: 'joy', label: '喜悅', emoji: '😊', color: '#FCD34D' },
  { id: 'peace', label: '平靜', emoji: '😌', color: '#8B5CF6' },
  { id: 'love', label: '愛', emoji: '💖', color: '#C084FC' },
  { id: 'hope', label: '希望', emoji: '🌟', color: '#10B981' },
  { id: 'growth', label: '成長', emoji: '🌱', color: '#6B46C1' },
];

const mockDiaries = [
  {
    id: 1,
    user: { name: '張小明', avatar: '👤' },
    time: '2小時前',
    content: '今天走在公園裡，看到夕陽西下的美景，心中充滿感恩。感謝大自然給予我們這樣美好的時刻...',
    emotion: 'gratitude',
    hasImage: true,
    hasAudio: false,
    likes: 12,
    comments: 3,
    isAnonymous: false,
  },
  {
    id: 2,
    user: { name: '匿名用戶', avatar: '🎭' },
    time: '5小時前',
    content: '感謝家人的陪伴，讓我在困難時刻感受到溫暖的力量。',
    emotion: 'love',
    hasImage: false,
    hasAudio: true,
    likes: 8,
    comments: 2,
    isAnonymous: true,
  },
  {
    id: 3,
    user: { name: '李小華', avatar: '👤' },
    time: '1天前',
    content: '今天完成了一個重要的項目，感謝團隊的協作和自己的努力。每一步成長都值得慶祝！',
    emotion: 'growth',
    hasImage: true,
    hasAudio: false,
    likes: 15,
    comments: 5,
    isAnonymous: false,
  },
  {
    id: 4,
    user: { name: '王小美', avatar: '👤' },
    time: '2天前',
    content: '早晨的第一縷陽光透過窗戶，帶來了新的希望和可能性。',
    emotion: 'hope',
    hasImage: false,
    hasAudio: false,
    likes: 6,
    comments: 1,
    isAnonymous: false,
  },
];

const HomePage = () => {
  const [selectedEmotion, setSelectedEmotion] = useState('all');

  const getEmotionColor = (emotionId) => {
    const emotion = emotionFilters.find(e => e.id === emotionId);
    return emotion ? emotion.color : '#6B7280';
  };

  const DiaryCard = ({ diary, index }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
    >
      <Card
        sx={{
          mb: 2,
          borderRadius: 4,
          border: `2px solid ${getEmotionColor(diary.emotion)}20`,
          position: 'relative',
          overflow: 'visible',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: -1,
            left: -1,
            right: -1,
            bottom: -1,
            borderRadius: 4,
            background: `linear-gradient(45deg, ${getEmotionColor(diary.emotion)}40, transparent)`,
            zIndex: -1,
          },
        }}
      >
        <CardContent sx={{ p: 2 }}>
          {/* 用戶信息 */}
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Avatar sx={{ width: 32, height: 32, mr: 1, fontSize: '1rem' }}>
              {diary.user.avatar}
            </Avatar>
            <Box sx={{ flex: 1 }}>
              <Typography variant="body2" fontWeight={600}>
                {diary.isAnonymous ? '匿名' : diary.user.name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {diary.time}
              </Typography>
            </Box>
            {/* 媒體指示器 */}
            <Box sx={{ display: 'flex', gap: 0.5 }}>
              {diary.hasImage && (
                <ImageIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
              )}
              {diary.hasAudio && (
                <MicIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
              )}
            </Box>
          </Box>

          {/* 內容 */}
          <Typography
            variant="body2"
            sx={{
              mb: 2,
              lineHeight: 1.5,
              color: 'text.primary',
            }}
          >
            {diary.content}
          </Typography>

          {/* 底部操作區 */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            {/* 情感標籤 */}
            <Chip
              label={emotionFilters.find(e => e.id === diary.emotion)?.label}
              size="small"
              sx={{
                bgcolor: `${getEmotionColor(diary.emotion)}20`,
                color: getEmotionColor(diary.emotion),
                fontWeight: 600,
                fontSize: '0.75rem',
              }}
            />

            {/* 互動按鈕 */}
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                size="small"
                startIcon={<FavoriteIcon />}
                sx={{ minWidth: 'auto', color: 'text.secondary', fontSize: '0.75rem' }}
              >
                {diary.likes}
              </Button>
              <Button
                size="small"
                startIcon={<ChatBubbleOutlineIcon />}
                sx={{ minWidth: 'auto', color: 'text.secondary', fontSize: '0.75rem' }}
              >
                {diary.comments}
              </Button>
              <IconButton size="small" sx={{ color: 'text.secondary' }}>
                <ShareIcon fontSize="small" />
              </IconButton>
            </Box>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );

  return (
    <Box sx={{
      bgcolor: 'background.default',
      minHeight: '100vh',
      width: '100%',
      maxWidth: '100vw',
      margin: '0 auto',
    }}>
      {/* 頂部導航欄 */}
      <AppBar
        position="sticky"
        elevation={0}
        sx={{
          bgcolor: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(0,0,0,0.1)',
        }}
      >
        <Toolbar sx={{
          justifyContent: 'space-between',
          maxWidth: '800px',
          width: '100%',
          margin: '0 auto',
        }}>
          <IconButton>
            <SearchIcon sx={{ color: 'primary.main' }} />
          </IconButton>
          <Typography
            variant="h3"
            sx={{
              color: 'text.primary',
              fontWeight: 600,
            }}
          >
            感恩日記
          </Typography>
          <IconButton>
            <FilterListIcon sx={{ color: 'primary.main' }} />
          </IconButton>
        </Toolbar>
      </AppBar>

      {/* 情感篩選條 */}
      <Box
        sx={{
          p: 2,
          bgcolor: 'background.paper',
          borderBottom: '1px solid rgba(0,0,0,0.1)',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            gap: 1,
            overflowX: 'auto',
            pb: 1,
            maxWidth: '800px',
            width: '100%',
            margin: '0 auto',
            '&::-webkit-scrollbar': { display: 'none' },
          }}
        >
          {emotionFilters.map((emotion) => (
            <Chip
              key={emotion.id}
              label={`${emotion.emoji} ${emotion.label}`}
              onClick={() => setSelectedEmotion(emotion.id)}
              variant={selectedEmotion === emotion.id ? 'filled' : 'outlined'}
              sx={{
                minWidth: 'auto',
                bgcolor: selectedEmotion === emotion.id ? `${emotion.color}20` : 'transparent',
                borderColor: emotion.color,
                color: selectedEmotion === emotion.id ? emotion.color : 'text.secondary',
                fontWeight: selectedEmotion === emotion.id ? 600 : 400,
                '&:hover': {
                  bgcolor: `${emotion.color}10`,
                },
              }}
            />
          ))}
        </Box>
      </Box>

      {/* 卡片牆內容 */}
      <Box sx={{
        p: 2,
        maxWidth: '800px',
        width: '100%',
        margin: '0 auto',
      }}>
        <Grid container spacing={1}>
          {mockDiaries.map((diary, index) => (
            <Grid item xs={6} sm={4} md={3} key={diary.id}>
              <DiaryCard diary={diary} index={index} />
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );
};

export default HomePage;
