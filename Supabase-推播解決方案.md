# Supabase 推播通知解決方案

## 問題分析
Supabase本身不提供推播通知服務，需要整合第三方解決方案。

## 🔧 推薦解決方案

### 方案一：OneSignal（推薦）
**優點**：免費額度高、多平台支援、易於整合
**缺點**：需要額外學習成本

```javascript
// OneSignal整合範例
import OneSignal from 'react-onesignal';

// 初始化OneSignal
await OneSignal.init({
  appId: "your-onesignal-app-id",
  safari_web_id: "web.onesignal.auto.your-id",
  notifyButton: {
    enable: true,
  },
});

// 發送推播
const sendNotification = async (userId, title, message) => {
  const notification = {
    app_id: "your-onesignal-app-id",
    include_external_user_ids: [userId],
    headings: { en: title },
    contents: { en: message },
    data: { type: 'diary_reminder' }
  };

  const response = await fetch('https://onesignal.com/api/v1/notifications', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Basic ${process.env.ONESIGNAL_REST_API_KEY}`
    },
    body: JSON.stringify(notification)
  });

  return response.json();
};
```

### 方案二：Expo Push Notifications
**適用於**：React Native應用
**優點**：與Expo生態系統完美整合

```javascript
// Expo Push整合
import * as Notifications from 'expo-notifications';

// 註冊推播token
const registerForPushNotifications = async () => {
  const { status } = await Notifications.requestPermissionsAsync();
  
  if (status !== 'granted') {
    throw new Error('推播權限被拒絕');
  }

  const token = (await Notifications.getExpoPushTokenAsync()).data;
  
  // 保存token到Supabase
  await supabase
    .from('user_push_tokens')
    .upsert({ user_id: userId, token });

  return token;
};

// 發送推播（後端）
const sendExpoPushNotification = async (tokens, title, body) => {
  const messages = tokens.map(token => ({
    to: token,
    sound: 'default',
    title,
    body,
    data: { type: 'diary_reminder' }
  }));

  const response = await fetch('https://exp.host/--/api/v2/push/send', {
    method: 'POST',
    headers: {
      'Accept': 'application/json',
      'Accept-encoding': 'gzip, deflate',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(messages),
  });

  return response.json();
};
```

### 方案三：Firebase Cloud Messaging（混合方案）
**說明**：只使用FCM推播，其他服務用Supabase

```javascript
// 混合架構：Supabase + FCM
import { initializeApp } from 'firebase/app';
import { getMessaging, getToken } from 'firebase/messaging';

// 只初始化Firebase Messaging
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  messagingSenderId: "your-sender-id",
  appId: "your-app-id"
};

const app = initializeApp(firebaseConfig);
const messaging = getMessaging(app);

// 獲取FCM token
const getFCMToken = async () => {
  const token = await getToken(messaging, {
    vapidKey: 'your-vapid-key'
  });
  
  // 保存到Supabase
  await supabase
    .from('user_fcm_tokens')
    .upsert({ user_id: userId, fcm_token: token });
    
  return token;
};
```

## 🏗️ Supabase架構調整

### 1. 資料庫Schema設計
```sql
-- 用戶推播token表
CREATE TABLE user_push_tokens (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  platform VARCHAR(20) NOT NULL, -- 'web', 'ios', 'android'
  token TEXT NOT NULL,
  provider VARCHAR(20) NOT NULL, -- 'onesignal', 'fcm', 'expo'
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 通知記錄表
CREATE TABLE notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  type VARCHAR(50) NOT NULL,
  data JSONB,
  is_read BOOLEAN DEFAULT false,
  sent_at TIMESTAMP WITH TIME ZONE,
  read_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 推播發送記錄表
CREATE TABLE push_notifications_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  notification_id UUID REFERENCES notifications(id),
  token TEXT NOT NULL,
  provider VARCHAR(20) NOT NULL,
  status VARCHAR(20) NOT NULL, -- 'sent', 'failed', 'delivered'
  response_data JSONB,
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2. Edge Functions實現推播
```typescript
// supabase/functions/send-notification/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const supabase = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

serve(async (req) => {
  try {
    const { userId, title, content, type, data } = await req.json()

    // 1. 創建通知記錄
    const { data: notification, error: notificationError } = await supabase
      .from('notifications')
      .insert({
        user_id: userId,
        title,
        content,
        type,
        data
      })
      .select()
      .single()

    if (notificationError) throw notificationError

    // 2. 獲取用戶的推播tokens
    const { data: tokens, error: tokensError } = await supabase
      .from('user_push_tokens')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)

    if (tokensError) throw tokensError

    // 3. 根據不同provider發送推播
    const sendPromises = tokens.map(async (tokenRecord) => {
      let result;
      
      switch (tokenRecord.provider) {
        case 'onesignal':
          result = await sendOneSignalNotification(tokenRecord.token, title, content, data)
          break
        case 'fcm':
          result = await sendFCMNotification(tokenRecord.token, title, content, data)
          break
        case 'expo':
          result = await sendExpoNotification(tokenRecord.token, title, content, data)
          break
        default:
          throw new Error(`不支援的推播provider: ${tokenRecord.provider}`)
      }

      // 記錄發送結果
      await supabase
        .from('push_notifications_log')
        .insert({
          notification_id: notification.id,
          token: tokenRecord.token,
          provider: tokenRecord.provider,
          status: result.success ? 'sent' : 'failed',
          response_data: result
        })

      return result
    })

    const results = await Promise.allSettled(sendPromises)
    
    return new Response(
      JSON.stringify({
        success: true,
        notification_id: notification.id,
        sent_count: results.filter(r => r.status === 'fulfilled').length,
        failed_count: results.filter(r => r.status === 'rejected').length
      }),
      { headers: { "Content-Type": "application/json" } }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 400, headers: { "Content-Type": "application/json" } }
    )
  }
})

// OneSignal發送函數
async function sendOneSignalNotification(token: string, title: string, content: string, data: any) {
  const response = await fetch('https://onesignal.com/api/v1/notifications', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Basic ${Deno.env.get('ONESIGNAL_REST_API_KEY')}`
    },
    body: JSON.stringify({
      app_id: Deno.env.get('ONESIGNAL_APP_ID'),
      include_player_ids: [token],
      headings: { en: title },
      contents: { en: content },
      data
    })
  })

  return await response.json()
}

// FCM發送函數
async function sendFCMNotification(token: string, title: string, content: string, data: any) {
  const response = await fetch('https://fcm.googleapis.com/fcm/send', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `key=${Deno.env.get('FCM_SERVER_KEY')}`
    },
    body: JSON.stringify({
      to: token,
      notification: { title, body: content },
      data
    })
  })

  return await response.json()
}

// Expo發送函數
async function sendExpoNotification(token: string, title: string, content: string, data: any) {
  const response = await fetch('https://exp.host/--/api/v2/push/send', {
    method: 'POST',
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      to: token,
      title,
      body: content,
      data
    })
  })

  return await response.json()
}
```

## 📱 前端整合調整

### React整合範例
```javascript
// src/services/notificationService.js
import { supabase } from '../config/supabase'
import OneSignal from 'react-onesignal'

class NotificationService {
  async initialize() {
    // 初始化OneSignal
    await OneSignal.init({
      appId: process.env.REACT_APP_ONESIGNAL_APP_ID,
    })

    // 獲取用戶ID和token
    const userId = await OneSignal.getUserId()
    const { data: { user } } = await supabase.auth.getUser()

    if (user && userId) {
      // 保存token到Supabase
      await this.saveToken(user.id, userId, 'onesignal', 'web')
    }
  }

  async saveToken(userId, token, provider, platform) {
    const { error } = await supabase
      .from('user_push_tokens')
      .upsert({
        user_id: userId,
        token,
        provider,
        platform,
        is_active: true
      })

    if (error) {
      console.error('保存推播token失敗:', error)
    }
  }

  async sendNotification(userId, title, content, type = 'general', data = {}) {
    const { data, error } = await supabase.functions.invoke('send-notification', {
      body: { userId, title, content, type, data }
    })

    if (error) {
      console.error('發送通知失敗:', error)
      return { success: false, error }
    }

    return { success: true, data }
  }
}

export const notificationService = new NotificationService()
```

## 💰 成本比較

### Firebase方案
- 免費額度：每月10,000則推播
- 付費：$0.50/1000則推播

### OneSignal方案
- 免費額度：每月10,000則推播
- 付費：$9/月起（無限推播）

### Expo方案
- 完全免費（有合理使用限制）

## 🎯 建議方案

**推薦使用 Supabase + OneSignal 組合**：
1. ✅ 成本效益高
2. ✅ 功能完整
3. ✅ 多平台支援
4. ✅ 易於維護
5. ✅ 擴展性好

這樣您可以享受Supabase的PostgreSQL強大功能，同時解決推播通知的需求。
