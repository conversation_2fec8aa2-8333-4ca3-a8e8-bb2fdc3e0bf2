// 多語言支援工具
// 這是一個簡單的國際化工具，為將來的完整多語言支援做準備

// 語言配置
export const LANGUAGES = {
  'en': {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    direction: 'ltr',
  },
  'zh-TW': {
    code: 'zh-TW',
    name: '繁體中文',
    nativeName: '繁體中文',
    flag: '🇹🇼',
    direction: 'ltr',
  },
  'zh-CN': {
    code: 'zh-CN',
    name: '簡體中文',
    nativeName: '简体中文',
    flag: '🇨🇳',
    direction: 'ltr',
  },
};

// 預設語言
export const DEFAULT_LANGUAGE = 'zh-TW';

// 獲取當前語言
export const getCurrentLanguage = () => {
  return localStorage.getItem('app_language') || DEFAULT_LANGUAGE;
};

// 設定語言
export const setLanguage = (languageCode) => {
  if (LANGUAGES[languageCode]) {
    localStorage.setItem('app_language', languageCode);
    // 觸發語言變更事件
    window.dispatchEvent(new CustomEvent('languageChanged', { 
      detail: { language: languageCode } 
    }));
    return true;
  }
  return false;
};

// 獲取語言信息
export const getLanguageInfo = (languageCode) => {
  return LANGUAGES[languageCode] || LANGUAGES[DEFAULT_LANGUAGE];
};

// 獲取所有可用語言
export const getAvailableLanguages = () => {
  return Object.values(LANGUAGES);
};

// 簡單的翻譯函數（為將來的完整實現做準備）
export const t = (key, fallback = key) => {
  // 目前返回 fallback，將來可以實現完整的翻譯邏輯
  return fallback;
};

// 格式化日期（根據語言）
export const formatDate = (date, languageCode = getCurrentLanguage()) => {
  const options = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  };
  
  const locales = {
    'en': 'en-US',
    'zh-TW': 'zh-TW',
    'zh-CN': 'zh-CN',
  };
  
  return new Intl.DateTimeFormat(locales[languageCode] || locales[DEFAULT_LANGUAGE], options)
    .format(new Date(date));
};

// 格式化數字（根據語言）
export const formatNumber = (number, languageCode = getCurrentLanguage()) => {
  const locales = {
    'en': 'en-US',
    'zh-TW': 'zh-TW',
    'zh-CN': 'zh-CN',
  };
  
  return new Intl.NumberFormat(locales[languageCode] || locales[DEFAULT_LANGUAGE])
    .format(number);
};

// 檢測瀏覽器語言
export const detectBrowserLanguage = () => {
  const browserLang = navigator.language || navigator.userLanguage;
  
  // 檢查是否有完全匹配的語言
  if (LANGUAGES[browserLang]) {
    return browserLang;
  }
  
  // 檢查語言前綴匹配
  const langPrefix = browserLang.split('-')[0];
  const matchingLang = Object.keys(LANGUAGES).find(lang => 
    lang.startsWith(langPrefix)
  );
  
  return matchingLang || DEFAULT_LANGUAGE;
};

// 初始化語言設定
export const initializeLanguage = () => {
  const savedLanguage = getCurrentLanguage();
  const detectedLanguage = detectBrowserLanguage();
  
  // 如果沒有保存的語言設定，使用檢測到的語言
  if (!localStorage.getItem('app_language')) {
    setLanguage(detectedLanguage);
    return detectedLanguage;
  }
  
  return savedLanguage;
};
