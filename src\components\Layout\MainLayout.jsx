import React from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { Box, BottomNavigation, BottomNavigationAction, Paper } from '@mui/material';
import HomeIcon from '@mui/icons-material/Home';
import EditIcon from '@mui/icons-material/Edit';
import CenterFocusStrongIcon from '@mui/icons-material/CenterFocusStrong';
import PersonIcon from '@mui/icons-material/Person';

const MainLayout = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // 獲取當前路徑對應的tab值
  const getCurrentTab = () => {
    const path = location.pathname;
    if (path === '/app' || path === '/app/') return 0;
    if (path === '/app/create') return 1;
    if (path === '/app/focus') return 2;
    if (path === '/app/profile') return 3;
    return 0;
  };

  const handleTabChange = (event, newValue) => {
    const routes = ['/app', '/app/create', '/app/focus', '/app/profile'];
    navigate(routes[newValue]);
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        width: '100vw',
        maxWidth: '100%',
        bgcolor: 'background.default',
        margin: '0 auto',
        position: 'relative',
      }}
    >
      {/* 主要內容區域 */}
      <Box
        sx={{
          flex: 1,
          pb: 10, // 為底部導航留出空間
          overflowY: 'auto',
          overflowX: 'hidden',
          width: '100%',
        }}
      >
        <Outlet />
      </Box>

      {/* 底部導航欄 */}
      <Paper
        sx={{
          position: 'fixed',
          bottom: 0,
          left: 0,
          right: 0,
          zIndex: 1000,
          borderRadius: 0,
          boxShadow: '0 -2px 8px rgba(0,0,0,0.1)',
        }}
        elevation={3}
      >
        <BottomNavigation
          value={getCurrentTab()}
          onChange={handleTabChange}
          sx={{
            height: 80,
            '& .MuiBottomNavigationAction-root': {
              color: 'text.secondary',
              '&.Mui-selected': {
                color: 'primary.main',
              },
            },
          }}
        >
          <BottomNavigationAction
            label="首頁"
            icon={<HomeIcon />}
            sx={{
              '& .MuiBottomNavigationAction-label': {
                fontSize: '0.75rem',
                fontWeight: 500,
              },
            }}
          />
          <BottomNavigationAction
            label="記錄"
            icon={<EditIcon />}
            sx={{
              '& .MuiBottomNavigationAction-label': {
                fontSize: '0.75rem',
                fontWeight: 500,
              },
            }}
          />
          <BottomNavigationAction
            label="專注"
            icon={<CenterFocusStrongIcon />}
            sx={{
              '& .MuiBottomNavigationAction-label': {
                fontSize: '0.75rem',
                fontWeight: 500,
              },
            }}
          />
          <BottomNavigationAction
            label="我的"
            icon={<PersonIcon />}
            sx={{
              '& .MuiBottomNavigationAction-label': {
                fontSize: '0.75rem',
                fontWeight: 500,
              },
            }}
          />
        </BottomNavigation>
      </Paper>
    </Box>
  );
};

export default MainLayout;
