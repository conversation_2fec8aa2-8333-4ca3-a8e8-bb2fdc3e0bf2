rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    
    // 輔助函數
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function isValidImageFile() {
      return request.resource.contentType.matches('image/.*') &&
             request.resource.size < 10 * 1024 * 1024; // 10MB限制
    }
    
    function isValidAudioFile() {
      return request.resource.contentType.matches('audio/.*') &&
             request.resource.size < 20 * 1024 * 1024; // 20MB限制
    }
    
    function isValidMediaFile() {
      return isValidImageFile() || isValidAudioFile();
    }
    
    // 用戶頭像
    match /avatars/{userId}/{allPaths=**} {
      allow read: if true; // 頭像公開可讀
      
      allow write: if isAuthenticated() && 
                   isOwner(userId) && 
                   isValidImageFile();
      
      allow delete: if isAuthenticated() && isOwner(userId);
    }
    
    // 日記媒體文件
    match /entries/{userId}/{entryId}/{allPaths=**} {
      allow read: if isAuthenticated(); // 需要登入才能查看
      
      allow write: if isAuthenticated() && 
                   isOwner(userId) && 
                   isValidMediaFile();
      
      allow delete: if isAuthenticated() && isOwner(userId);
    }
    
    // 臨時上傳文件（用於處理中的文件）
    match /temp/{userId}/{allPaths=**} {
      allow read, write: if isAuthenticated() && 
                        isOwner(userId) && 
                        isValidMediaFile();
      
      // 臨時文件24小時後自動刪除（通過Cloud Function實現）
    }
    
    // 系統資源（圖標、預設圖片等）
    match /system/{allPaths=**} {
      allow read: if true; // 系統資源公開可讀
      allow write: if false; // 只有管理員可以寫入（通過Admin SDK）
    }
    
    // 備份文件（只有系統可以訪問）
    match /backups/{allPaths=**} {
      allow read, write: if false; // 只有Admin SDK可以訪問
    }
    
    // 縮圖和處理後的圖片
    match /thumbnails/{userId}/{entryId}/{allPaths=**} {
      allow read: if isAuthenticated();
      allow write: if false; // 只有Cloud Function可以生成縮圖
    }
    
    // 用戶導出的數據
    match /exports/{userId}/{allPaths=**} {
      allow read: if isAuthenticated() && isOwner(userId);
      allow write: if false; // 只有Cloud Function可以生成導出文件
      allow delete: if isAuthenticated() && isOwner(userId);
    }
  }
}
