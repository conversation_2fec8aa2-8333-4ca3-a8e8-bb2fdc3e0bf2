# Firebase 建置指南

## 專案概述

本指南將詳細說明如何使用Firebase建置感恩日記應用的後端服務，包括認證、數據庫、存儲、雲函數等完整的後端架構。

## 前置準備

### 1. 環境要求
- Node.js 18+ 
- npm 或 yarn
- Firebase CLI
- Google Cloud Platform 帳戶

### 2. 安裝Firebase CLI
```bash
npm install -g firebase-tools
firebase login
```

### 3. 創建Firebase專案
1. 前往 [Firebase Console](https://console.firebase.google.com/)
2. 點擊「新增專案」
3. 輸入專案名稱：`gratitude-diary`
4. 啟用Google Analytics（可選）
5. 選擇Analytics帳戶

## Firebase專案初始化

### 1. 初始化Firebase專案
```bash
# 在專案根目錄執行
firebase init

# 選擇需要的服務：
# ✓ Authentication
# ✓ Firestore
# ✓ Functions
# ✓ Hosting
# ✓ Storage
# ✓ Emulators
```

### 2. 專案結構
```
gratitude-diary/
├── functions/                 # Cloud Functions
│   ├── src/
│   │   ├── index.ts
│   │   ├── auth/
│   │   ├── entries/
│   │   ├── users/
│   │   └── utils/
│   ├── package.json
│   └── tsconfig.json
├── firestore.rules           # Firestore安全規則
├── firestore.indexes.json    # Firestore索引
├── storage.rules             # Storage安全規則
├── firebase.json             # Firebase配置
└── .firebaserc              # Firebase專案配置
```

## Firebase Authentication 設定

### 1. 啟用認證方式
在Firebase Console中啟用以下認證方式：
- 電子郵件/密碼
- Google
- Facebook
- Apple（iOS應用）

### 2. 認證配置
```javascript
// firebase-config.js
import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';

const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "gratitude-diary.firebaseapp.com",
  projectId: "gratitude-diary",
  storageBucket: "gratitude-diary.appspot.com",
  messagingSenderId: "123456789",
  appId: "your-app-id"
};

const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);
```

### 3. 用戶註冊函數
```typescript
// functions/src/auth/register.ts
import { auth } from 'firebase-admin';
import { db } from '../utils/admin';

export const registerUser = async (email: string, password: string, displayName: string) => {
  try {
    // 創建用戶
    const userRecord = await auth().createUser({
      email,
      password,
      displayName,
    });

    // 創建用戶文檔
    await db.collection('users').doc(userRecord.uid).set({
      uid: userRecord.uid,
      email,
      displayName,
      preferences: {
        language: 'zh-TW',
        timezone: 'Asia/Taipei',
        theme: 'light',
        privacy: {
          profileVisible: true,
          allowComments: true,
          allowMessages: false
        },
        notifications: {
          dailyReminder: true,
          focusReminder: true,
          socialInteraction: true,
          pushEnabled: true
        }
      },
      stats: {
        totalEntries: 0,
        totalLikes: 0,
        totalComments: 0,
        focusSessions: 0,
        focusMinutes: 0,
        streakDays: 0,
        joinDate: new Date(),
        lastActiveDate: new Date()
      },
      achievements: [],
      createdAt: new Date(),
      updatedAt: new Date()
    });

    return { success: true, uid: userRecord.uid };
  } catch (error) {
    throw new Error(`註冊失敗: ${error.message}`);
  }
};
```

## Cloud Firestore 設定

### 1. 數據庫結構
```
/users/{userId}
/entries/{entryId}
/interactions/{interactionId}
/focusSessions/{sessionId}
/notifications/{notificationId}
/achievements/{achievementId}
```

### 2. Firestore安全規則
```javascript
// firestore.rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // 用戶文檔規則
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null && 
        resource.data.preferences.privacy.profileVisible == true;
    }
    
    // 日記條目規則
    match /entries/{entryId} {
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
      allow read: if request.auth != null && (
        request.auth.uid == resource.data.userId ||
        resource.data.privacy == 'public' ||
        (resource.data.privacy == 'friends' && 
         request.auth.uid in resource.data.friendsList)
      );
      allow update, delete: if request.auth != null && 
        request.auth.uid == resource.data.userId;
    }
    
    // 互動記錄規則
    match /interactions/{interactionId} {
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
      allow read: if request.auth != null;
      allow delete: if request.auth != null && 
        request.auth.uid == resource.data.userId;
    }
    
    // 專注工作記錄規則
    match /focusSessions/{sessionId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
    }
    
    // 通知規則
    match /notifications/{notificationId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
    }
  }
}
```

### 3. Firestore索引配置
```json
{
  "indexes": [
    {
      "collectionGroup": "entries",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "userId", "order": "ASCENDING" },
        { "fieldPath": "createdAt", "order": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "entries",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "privacy", "order": "ASCENDING" },
        { "fieldPath": "emotion", "order": "ASCENDING" },
        { "fieldPath": "createdAt", "order": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "interactions",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "entryId", "order": "ASCENDING" },
        { "fieldPath": "type", "order": "ASCENDING" },
        { "fieldPath": "createdAt", "order": "DESCENDING" }
      ]
    }
  ],
  "fieldOverrides": []
}
```

## Cloud Functions 實作

### 1. 主要函數文件
```typescript
// functions/src/index.ts
import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import express from 'express';
import cors from 'cors';

// 初始化Firebase Admin
admin.initializeApp();

const app = express();
app.use(cors({ origin: true }));
app.use(express.json());

// 導入路由
import authRoutes from './auth/routes';
import userRoutes from './users/routes';
import entryRoutes from './entries/routes';
import focusRoutes from './focus/routes';

// 註冊路由
app.use('/auth', authRoutes);
app.use('/users', userRoutes);
app.use('/entries', entryRoutes);
app.use('/focus', focusRoutes);

// 導出API
export const api = functions.region('asia-east1').https.onRequest(app);

// 觸發器函數
export const onUserCreate = functions.auth.user().onCreate(async (user) => {
  // 用戶創建時的處理邏輯
});

export const onEntryCreate = functions.firestore
  .document('entries/{entryId}')
  .onCreate(async (snap, context) => {
    // 日記創建時的處理邏輯
  });
```

### 2. 用戶管理函數
```typescript
// functions/src/users/routes.ts
import { Router } from 'express';
import { authenticateUser } from '../middleware/auth';
import { getUserProfile, updateUserProfile, getUserStats } from './controller';

const router = Router();

router.get('/profile', authenticateUser, getUserProfile);
router.put('/profile', authenticateUser, updateUserProfile);
router.get('/stats', authenticateUser, getUserStats);

export default router;
```

### 3. 日記管理函數
```typescript
// functions/src/entries/controller.ts
import { Request, Response } from 'express';
import { db } from '../utils/admin';

export const createEntry = async (req: Request, res: Response) => {
  try {
    const { content, emotion, tags, privacy, isAnonymous } = req.body;
    const userId = req.user.uid;

    const entryData = {
      userId,
      content,
      emotion,
      tags: tags || [],
      privacy: privacy || 'public',
      isAnonymous: isAnonymous || false,
      media: [],
      interactions: {
        likes: 0,
        comments: 0,
        shares: 0
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const docRef = await db.collection('entries').add(entryData);
    
    // 更新用戶統計
    await db.collection('users').doc(userId).update({
      'stats.totalEntries': admin.firestore.FieldValue.increment(1),
      'stats.lastActiveDate': new Date(),
      updatedAt: new Date()
    });

    res.status(201).json({
      success: true,
      data: {
        entry: {
          id: docRef.id,
          ...entryData
        }
      },
      message: '日記創建成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: 'CREATE_ENTRY_ERROR',
        message: '創建日記失敗',
        details: error.message
      }
    });
  }
};
```

## Firebase Storage 設定

### 1. Storage安全規則
```javascript
// storage.rules
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // 用戶頭像
    match /avatars/{userId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == userId
        && request.resource.size < 10 * 1024 * 1024; // 10MB限制
    }
    
    // 日記媒體文件
    match /entries/{userId}/{entryId}/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId
        && request.resource.size < 20 * 1024 * 1024; // 20MB限制
    }
  }
}
```

### 2. 文件上傳函數
```typescript
// functions/src/storage/upload.ts
import { Request, Response } from 'express';
import { storage } from '../utils/admin';
import multer from 'multer';

const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 20 * 1024 * 1024 // 20MB
  }
});

export const uploadMedia = async (req: Request, res: Response) => {
  try {
    const file = req.file;
    const { entryId, type } = req.body;
    const userId = req.user.uid;

    if (!file) {
      return res.status(400).json({
        success: false,
        error: { message: '沒有上傳文件' }
      });
    }

    const fileName = `${Date.now()}_${file.originalname}`;
    const filePath = `entries/${userId}/${entryId}/${fileName}`;
    
    const bucket = storage.bucket();
    const fileRef = bucket.file(filePath);
    
    await fileRef.save(file.buffer, {
      metadata: {
        contentType: file.mimetype,
        metadata: {
          userId,
          entryId,
          type
        }
      }
    });

    const downloadURL = await fileRef.getSignedUrl({
      action: 'read',
      expires: '03-09-2491'
    });

    res.json({
      success: true,
      data: {
        url: downloadURL[0],
        fileName,
        size: file.size,
        type: file.mimetype
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: { message: '文件上傳失敗' }
    });
  }
};
```

## Firebase Cloud Messaging 設定

### 1. 推播通知配置
```typescript
// functions/src/notifications/fcm.ts
import { messaging } from 'firebase-admin';

export const sendNotification = async (
  token: string,
  title: string,
  body: string,
  data?: any
) => {
  try {
    const message = {
      notification: {
        title,
        body
      },
      data: data || {},
      token
    };

    const response = await messaging().send(message);
    return { success: true, messageId: response };
  } catch (error) {
    throw new Error(`推播發送失敗: ${error.message}`);
  }
};

// 批量發送通知
export const sendMulticastNotification = async (
  tokens: string[],
  title: string,
  body: string,
  data?: any
) => {
  try {
    const message = {
      notification: {
        title,
        body
      },
      data: data || {},
      tokens
    };

    const response = await messaging().sendMulticast(message);
    return {
      success: true,
      successCount: response.successCount,
      failureCount: response.failureCount
    };
  } catch (error) {
    throw new Error(`批量推播發送失敗: ${error.message}`);
  }
};
```

### 2. 通知觸發器
```typescript
// functions/src/triggers/notifications.ts
import * as functions from 'firebase-functions';
import { sendNotification } from '../notifications/fcm';
import { db } from '../utils/admin';

// 點讚通知
export const onLikeCreate = functions.firestore
  .document('interactions/{interactionId}')
  .onCreate(async (snap, context) => {
    const interaction = snap.data();

    if (interaction.type === 'like') {
      // 獲取被點讚的日記
      const entryDoc = await db.collection('entries').doc(interaction.entryId).get();
      const entry = entryDoc.data();

      if (entry && entry.userId !== interaction.userId) {
        // 獲取日記作者的FCM token
        const userDoc = await db.collection('users').doc(entry.userId).get();
        const user = userDoc.data();

        if (user && user.fcmToken && user.preferences.notifications.socialInteraction) {
          await sendNotification(
            user.fcmToken,
            '有人點讚了你的日記',
            '你的感恩分享獲得了新的點讚！',
            {
              type: 'like',
              entryId: interaction.entryId,
              fromUserId: interaction.userId
            }
          );
        }
      }
    }
  });

// 每日提醒
export const dailyReminder = functions.pubsub
  .schedule('0 20 * * *')
  .timeZone('Asia/Taipei')
  .onRun(async (context) => {
    // 獲取啟用每日提醒的用戶
    const usersSnapshot = await db.collection('users')
      .where('preferences.notifications.dailyReminder', '==', true)
      .where('fcmToken', '!=', null)
      .get();

    const tokens: string[] = [];
    usersSnapshot.forEach(doc => {
      const user = doc.data();
      if (user.fcmToken) {
        tokens.push(user.fcmToken);
      }
    });

    if (tokens.length > 0) {
      await sendMulticastNotification(
        tokens,
        '今天的感恩時光',
        '記錄今天讓你感恩的美好時刻吧！',
        { type: 'daily_reminder' }
      );
    }
  });
```

## 部署配置

### 1. Firebase配置文件
```json
{
  "functions": {
    "source": "functions",
    "runtime": "nodejs18",
    "region": "asia-east1"
  },
  "firestore": {
    "rules": "firestore.rules",
    "indexes": "firestore.indexes.json"
  },
  "storage": {
    "rules": "storage.rules"
  },
  "hosting": {
    "public": "dist",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "rewrites": [
      {
        "source": "/api/**",
        "function": "api"
      },
      {
        "source": "**",
        "destination": "/index.html"
      }
    ]
  },
  "emulators": {
    "auth": {
      "port": 9099
    },
    "functions": {
      "port": 5001
    },
    "firestore": {
      "port": 8080
    },
    "storage": {
      "port": 9199
    },
    "hosting": {
      "port": 5000
    },
    "ui": {
      "enabled": true,
      "port": 4000
    }
  }
}
```

### 2. 環境變數設定
```bash
# 設定Firebase Functions環境變數
firebase functions:config:set \
  app.name="感恩日記" \
  app.url="https://gratitude-diary.web.app" \
  email.from="<EMAIL>" \
  storage.bucket="gratitude-diary.appspot.com"

# 設定第三方服務密鑰
firebase functions:config:set \
  google.client_id="your-google-client-id" \
  facebook.app_id="your-facebook-app-id" \
  apple.key_id="your-apple-key-id"
```

### 3. 部署腳本
```bash
#!/bin/bash
# deploy.sh

echo "開始部署感恩日記應用..."

# 建置前端
echo "建置前端應用..."
npm run build

# 部署Firestore規則和索引
echo "部署Firestore規則..."
firebase deploy --only firestore

# 部署Storage規則
echo "部署Storage規則..."
firebase deploy --only storage

# 部署Cloud Functions
echo "部署Cloud Functions..."
firebase deploy --only functions

# 部署Hosting
echo "部署前端應用..."
firebase deploy --only hosting

echo "部署完成！"
echo "應用URL: https://gratitude-diary.web.app"
echo "API URL: https://asia-east1-gratitude-diary.cloudfunctions.net/api"
```

## 監控與維護

### 1. 性能監控
```typescript
// functions/src/utils/monitoring.ts
import { performance } from 'perf_hooks';

export const performanceLogger = (functionName: string) => {
  return (target: any, propertyName: string, descriptor: PropertyDescriptor) => {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const start = performance.now();

      try {
        const result = await method.apply(this, args);
        const end = performance.now();

        console.log(`${functionName}.${propertyName} 執行時間: ${end - start}ms`);
        return result;
      } catch (error) {
        const end = performance.now();
        console.error(`${functionName}.${propertyName} 執行失敗: ${end - start}ms`, error);
        throw error;
      }
    };
  };
};
```

### 2. 錯誤處理中間件
```typescript
// functions/src/middleware/errorHandler.ts
import { Request, Response, NextFunction } from 'express';

export const errorHandler = (
  error: any,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  console.error('API錯誤:', error);

  const statusCode = error.statusCode || 500;
  const message = error.message || '服務器內部錯誤';

  res.status(statusCode).json({
    success: false,
    error: {
      code: error.code || 'SERVER_ERROR',
      message,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    },
    timestamp: new Date().toISOString()
  });
};
```

### 3. 數據備份策略
```typescript
// functions/src/backup/scheduler.ts
import * as functions from 'firebase-functions';
import { firestore } from 'firebase-admin';

// 每日數據備份
export const dailyBackup = functions.pubsub
  .schedule('0 2 * * *')
  .timeZone('Asia/Taipei')
  .onRun(async (context) => {
    const client = new firestore.v1.FirestoreAdminClient();
    const projectId = process.env.GCLOUD_PROJECT;
    const databaseName = client.databasePath(projectId, '(default)');

    const bucket = `gs://${projectId}-backup`;
    const timestamp = new Date().toISOString().split('T')[0];

    try {
      const [operation] = await client.exportDocuments({
        name: databaseName,
        outputUriPrefix: `${bucket}/${timestamp}`,
        collectionIds: ['users', 'entries', 'interactions', 'focusSessions']
      });

      console.log(`備份操作已啟動: ${operation.name}`);
    } catch (error) {
      console.error('備份失敗:', error);
    }
  });
```

## 測試配置

### 1. 單元測試設定
```typescript
// functions/src/test/setup.ts
import * as admin from 'firebase-admin';
import { initializeTestEnvironment } from '@firebase/rules-unit-testing';

// 初始化測試環境
export const setupTestEnvironment = async () => {
  const testEnv = await initializeTestEnvironment({
    projectId: 'gratitude-diary-test',
    firestore: {
      rules: `
        rules_version = '2';
        service cloud.firestore {
          match /databases/{database}/documents {
            match /{document=**} {
              allow read, write: if true;
            }
          }
        }
      `
    }
  });

  return testEnv;
};
```

### 2. API測試
```typescript
// functions/src/test/api.test.ts
import request from 'supertest';
import { app } from '../index';

describe('API測試', () => {
  test('用戶註冊', async () => {
    const response = await request(app)
      .post('/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'password123',
        displayName: '測試用戶'
      });

    expect(response.status).toBe(201);
    expect(response.body.success).toBe(true);
  });

  test('創建日記', async () => {
    const token = 'test-jwt-token';

    const response = await request(app)
      .post('/entries')
      .set('Authorization', `Bearer ${token}`)
      .send({
        content: '測試日記內容',
        emotion: 'gratitude',
        privacy: 'public'
      });

    expect(response.status).toBe(201);
    expect(response.body.data.entry).toBeDefined();
  });
});
```

## 安全性最佳實踐

### 1. API安全
- 實施請求限流
- 輸入數據驗證
- SQL注入防護
- XSS攻擊防護

### 2. 數據安全
- 敏感數據加密
- 定期安全審計
- 訪問日誌記錄
- 異常行為監控

### 3. 隱私保護
- GDPR合規
- 數據最小化原則
- 用戶同意管理
- 數據刪除權利

這個Firebase建置指南提供了完整的後端架構實施方案，支援多平台應用接入，具備良好的擴展性和安全性。
```
