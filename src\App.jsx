import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Box } from '@mui/material';

// 導入頁面組件
import SplashScreen from './pages/SplashScreen';
import OnboardingScreen from './pages/OnboardingScreen';
import HomePage from './pages/HomePage';
import CreateDiaryPage from './pages/CreateDiaryPage';
import FocusPage from './pages/FocusPage';
import ProfilePage from './pages/ProfilePage';
import SettingsPage from './pages/SettingsPage';

// 導入設定子頁面
import PersonalProfilePage from './pages/settings/PersonalProfilePage';
import NotificationSettingsPage from './pages/settings/NotificationSettingsPage';
import ThemeSettingsPage from './pages/settings/ThemeSettingsPage';
import HelpCenterPage from './pages/settings/HelpCenterPage';
import PrivacySettingsPage from './pages/settings/PrivacySettingsPage';
import DarkModeSettingsPage from './pages/settings/DarkModeSettingsPage';
import AboutPage from './pages/settings/AboutPage';
import LanguageSettingsPage from './pages/settings/LanguageSettingsPage';

// 導入個人中心子頁面
import MyDiariesPage from './pages/profile/MyDiariesPage';
import DataAnalysisPage from './pages/profile/DataAnalysisPage';
import FeedbackPage from './pages/profile/FeedbackPage';

// 導入佈局組件
import MainLayout from './components/Layout/MainLayout';

// 創建主題
const theme = createTheme({
  palette: {
    primary: {
      main: '#6B46C1', // 主紫色
      light: '#A78BFA', // 輔助紫色
      dark: '#553C9A',
    },
    secondary: {
      main: '#F59E0B', // 主金色
      light: '#FCD34D', // 輔助金色
      dark: '#D97706',
    },
    background: {
      default: '#F9FAFB', // 淺灰背景
      paper: '#FFFFFF', // 白色卡片背景
    },
    text: {
      primary: '#1F2937', // 深灰文字
      secondary: '#6B7280', // 中灰文字
    },
    emotion: {
      gratitude: '#F59E0B', // 感恩/滿足
      joy: '#FCD34D', // 喜悅
      peace: '#8B5CF6', // 平靜
      love: '#C084FC', // 愛/溫暖
      hope: '#10B981', // 希望
      growth: '#6B46C1', // 成長
    }
  },
  typography: {
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      'SF Pro Display',
      'SF Pro Text',
      'Segoe UI',
      'Roboto',
      'sans-serif'
    ].join(','),
    h1: {
      fontSize: '2rem',
      fontWeight: 700,
    },
    h2: {
      fontSize: '1.5rem',
      fontWeight: 600,
    },
    h3: {
      fontSize: '1.25rem',
      fontWeight: 600,
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.5,
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.4,
    },
    caption: {
      fontSize: '0.75rem',
    }
  },
  shape: {
    borderRadius: 12,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          textTransform: 'none',
          fontWeight: 600,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        },
      },
    },
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Box sx={{
          minHeight: '100vh',
          width: '100vw',
          maxWidth: '100%',
          bgcolor: 'background.default',
          margin: 0,
          padding: 0,
          overflowX: 'hidden',
        }}>
          <Routes>
            <Route path="/" element={<SplashScreen />} />
            <Route path="/onboarding" element={<OnboardingScreen />} />
            <Route path="/app" element={<MainLayout />}>
              <Route index element={<HomePage />} />
              <Route path="create" element={<CreateDiaryPage />} />
              <Route path="focus" element={<FocusPage />} />
              <Route path="profile" element={<ProfilePage />} />
              <Route path="settings" element={<SettingsPage />} />
              <Route path="settings/profile" element={<PersonalProfilePage />} />
              <Route path="settings/notifications" element={<NotificationSettingsPage />} />
              <Route path="settings/theme" element={<ThemeSettingsPage />} />
              <Route path="settings/help" element={<HelpCenterPage />} />
              <Route path="settings/privacy" element={<PrivacySettingsPage />} />
              <Route path="settings/darkmode" element={<DarkModeSettingsPage />} />
              <Route path="settings/language" element={<LanguageSettingsPage />} />
              <Route path="settings/about" element={<AboutPage />} />
              <Route path="profile/diaries" element={<MyDiariesPage />} />
              <Route path="profile/analytics" element={<DataAnalysisPage />} />
              <Route path="profile/feedback" element={<FeedbackPage />} />
            </Route>
          </Routes>
        </Box>
      </Router>
    </ThemeProvider>
  );
}

export default App;
