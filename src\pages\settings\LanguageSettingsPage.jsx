import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  AppBar,
  Toolbar,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Card,
  Divider,
  RadioGroup,
  FormControlLabel,
  Radio,
  Alert,
  Chip,
  Button,
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import LanguageIcon from '@mui/icons-material/Language';
import TranslateIcon from '@mui/icons-material/Translate';
import PublicIcon from '@mui/icons-material/Public';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { motion } from 'framer-motion';
import {
  getCurrentLanguage,
  setLanguage,
  getAvailableLanguages,
  getLanguageInfo
} from '../../utils/i18n';

const LanguageSettingsPage = () => {
  const navigate = useNavigate();
  const [selectedLanguage, setSelectedLanguage] = useState(getCurrentLanguage());
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  const handleBack = () => {
    navigate('/app/settings');
  };

  const handleLanguageChange = (event) => {
    const newLanguage = event.target.value;
    setSelectedLanguage(newLanguage);

    // 保存語言設定
    setLanguage(newLanguage);

    // 顯示成功訊息
    setShowSuccessMessage(true);
    setTimeout(() => {
      setShowSuccessMessage(false);
    }, 3000);

    console.log('語言已切換至:', newLanguage);
  };

  const languages = [
    {
      code: 'en',
      name: '英文',
      nativeName: 'English',
      flag: '🇺🇸',
      description: 'Switch to English interface',
      status: 'available',
    },
    {
      code: 'zh-TW',
      name: '繁體中文',
      nativeName: '繁體中文',
      flag: '🇹🇼',
      description: '切換到繁體中文介面',
      status: 'current',
    },
    {
      code: 'zh-CN',
      name: '簡體中文',
      nativeName: '简体中文',
      flag: '🇨🇳',
      description: '切换到简体中文界面',
      status: 'available',
    },
  ];

  const languageFeatures = [
    {
      icon: <TranslateIcon />,
      title: '自動翻譯',
      subtitle: '自動翻譯用戶生成的內容',
      enabled: true,
    },
    {
      icon: <PublicIcon />,
      title: '地區格式',
      subtitle: '根據語言調整日期和數字格式',
      enabled: true,
    },
  ];

  const getLanguageDisplayName = (lang) => {
    if (selectedLanguage === 'en') {
      const englishNames = {
        'en': 'English',
        'zh-TW': 'Traditional Chinese',
        'zh-CN': 'Simplified Chinese'
      };
      return englishNames[lang.code] || lang.name;
    }
    return lang.name;
  };

  return (
    <Box sx={{
      bgcolor: 'background.default',
      minHeight: '100vh',
      width: '100%',
      maxWidth: '100vw',
      margin: '0 auto',
    }}>
      {/* 頂部導航欄 */}
      <AppBar
        position="sticky"
        elevation={0}
        sx={{
          bgcolor: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(0,0,0,0.1)',
        }}
      >
        <Toolbar sx={{ maxWidth: '600px', width: '100%', margin: '0 auto' }}>
          <IconButton onClick={handleBack} sx={{ mr: 2 }}>
            <ArrowBackIcon sx={{ color: 'text.primary' }} />
          </IconButton>
          <Typography
            variant="h3"
            sx={{
              flex: 1,
              color: 'text.primary',
              fontWeight: 600,
            }}
          >
            語言設定
          </Typography>
        </Toolbar>
      </AppBar>

      <Box sx={{
        p: 2,
        maxWidth: '600px',
        width: '100%',
        margin: '0 auto',
      }}>
        {/* 成功訊息 */}
        {showSuccessMessage && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Alert 
              severity="success" 
              sx={{ mb: 3, borderRadius: 3 }}
              icon={<CheckCircleIcon />}
            >
              <Typography variant="body2">
                語言設定已更新！部分更改將在重新啟動應用後生效。
              </Typography>
            </Alert>
          </motion.div>
        )}

        {/* 當前語言顯示 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card sx={{ 
            borderRadius: 3, 
            mb: 3,
            background: 'linear-gradient(135deg, #6B46C1 0%, #A78BFA 100%)',
            color: 'white',
          }}>
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <Typography variant="h2" sx={{ fontSize: '2rem', mb: 1 }}>
                {languages.find(lang => lang.code === selectedLanguage)?.flag}
              </Typography>
              <Typography variant="h6" sx={{ mb: 1, fontWeight: 600 }}>
                當前語言
              </Typography>
              <Typography variant="body1" sx={{ opacity: 0.9 }}>
                {languages.find(lang => lang.code === selectedLanguage)?.nativeName}
              </Typography>
            </Box>
          </Card>
        </motion.div>

        {/* 語言選擇 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Typography
            variant="h6"
            sx={{
              mb: 1,
              fontWeight: 600,
              color: 'text.secondary',
              fontSize: '0.875rem',
              textTransform: 'uppercase',
              letterSpacing: 0.5,
            }}
          >
            選擇語言
          </Typography>
          <Card sx={{ borderRadius: 3, mb: 3 }}>
            <RadioGroup
              value={selectedLanguage}
              onChange={handleLanguageChange}
            >
              {languages.map((language, index) => (
                <React.Fragment key={language.code}>
                  <ListItem sx={{ py: 2 }}>
                    <ListItemIcon sx={{ 
                      color: 'primary.main', 
                      minWidth: 40,
                      fontSize: '1.5rem'
                    }}>
                      {language.flag}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body1" sx={{ fontWeight: 500 }}>
                            {getLanguageDisplayName(language)}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            ({language.nativeName})
                          </Typography>
                          {language.code === selectedLanguage && (
                            <Chip
                              label="當前"
                              size="small"
                              sx={{
                                bgcolor: 'primary.light',
                                color: 'primary.main',
                                fontSize: '0.75rem',
                              }}
                            />
                          )}
                        </Box>
                      }
                      secondary={
                        <Typography variant="body2" color="text.secondary">
                          {language.description}
                        </Typography>
                      }
                    />
                    <FormControlLabel
                      value={language.code}
                      control={<Radio color="primary" />}
                      label=""
                      sx={{ mr: 0 }}
                    />
                  </ListItem>
                  {index < languages.length - 1 && (
                    <Divider variant="inset" component="li" />
                  )}
                </React.Fragment>
              ))}
            </RadioGroup>
          </Card>
        </motion.div>

        {/* 語言功能設定 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Typography
            variant="h6"
            sx={{
              mb: 1,
              fontWeight: 600,
              color: 'text.secondary',
              fontSize: '0.875rem',
              textTransform: 'uppercase',
              letterSpacing: 0.5,
            }}
          >
            語言功能
          </Typography>
          <Card sx={{ borderRadius: 3, mb: 3 }}>
            <List sx={{ py: 0 }}>
              {languageFeatures.map((feature, index) => (
                <React.Fragment key={index}>
                  <ListItem sx={{ py: 2 }}>
                    <ListItemIcon sx={{ color: 'primary.main', minWidth: 40 }}>
                      {feature.icon}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body1" sx={{ fontWeight: 500 }}>
                            {feature.title}
                          </Typography>
                          {feature.enabled && (
                            <Chip
                              label="已啟用"
                              size="small"
                              sx={{
                                bgcolor: 'success.light',
                                color: 'success.main',
                                fontSize: '0.75rem',
                              }}
                            />
                          )}
                        </Box>
                      }
                      secondary={
                        <Typography variant="body2" color="text.secondary">
                          {feature.subtitle}
                        </Typography>
                      }
                    />
                  </ListItem>
                  {index < languageFeatures.length - 1 && (
                    <Divider variant="inset" component="li" />
                  )}
                </React.Fragment>
              ))}
            </List>
          </Card>
        </motion.div>

        {/* 語言包信息 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Alert severity="info" sx={{ borderRadius: 3, mb: 3 }}>
            <Typography variant="body2">
              💡 語言包會根據您的選擇自動下載。某些翻譯可能需要網路連接才能完成。
            </Typography>
          </Alert>
        </motion.div>

        {/* 重置按鈕 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <Box sx={{ textAlign: 'center', mt: 4, mb: 2 }}>
            <Button
              variant="outlined"
              color="primary"
              startIcon={<LanguageIcon />}
              onClick={() => {
                const defaultLang = 'zh-TW';
                setSelectedLanguage(defaultLang);
                setLanguage(defaultLang);
                setShowSuccessMessage(true);
                setTimeout(() => setShowSuccessMessage(false), 3000);
              }}
              sx={{ borderRadius: 3 }}
            >
              重置為系統預設語言
            </Button>
          </Box>
        </motion.div>
      </Box>
    </Box>
  );
};

export default LanguageSettingsPage;
