# 多平台支援指南

## 概述

感恩日記應用設計為多平台支援的架構，可以同時為Web、iOS、Android、桌面應用等多種客戶端提供服務。本指南詳細說明如何實現跨平台的API調用和數據同步。

## 架構設計

### 1. 統一API層
```
┌─────────────────────────────────────────────────────────┐
│                    客戶端應用層                           │
├─────────────┬─────────────┬─────────────┬─────────────────┤
│   Web App   │  iOS App    │ Android App │  Desktop App    │
│  (React)    │  (Swift)    │  (Kotlin)   │  (Electron)     │
└─────────────┴─────────────┴─────────────┴─────────────────┘
                              │
┌─────────────────────────────────────────────────────────┐
│                    API Gateway                          │
│              (Firebase Functions)                       │
└─────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────┐
│                   後端服務層                             │
├─────────────┬─────────────┬─────────────┬─────────────────┤
│ Firebase    │ Cloud       │ Firebase    │ Firebase        │
│ Auth        │ Firestore   │ Storage     │ Messaging       │
└─────────────┴─────────────┴─────────────┴─────────────────┘
```

### 2. 平台特性支援
- **Web**: PWA支援、響應式設計、離線功能
- **iOS**: 原生UI、推播通知、Touch ID/Face ID
- **Android**: Material Design、推播通知、指紋識別
- **Desktop**: 系統托盤、快捷鍵、本地存儲

## Web平台 (PWA)

### 1. PWA配置
```json
// public/manifest.json
{
  "name": "感恩日記",
  "short_name": "感恩日記",
  "description": "記錄生活中的美好時刻",
  "start_url": "/",
  "display": "standalone",
  "theme_color": "#6B46C1",
  "background_color": "#F9FAFB",
  "orientation": "portrait",
  "icons": [
    {
      "src": "/icons/icon-72x72.png",
      "sizes": "72x72",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-96x96.png",
      "sizes": "96x96",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-128x128.png",
      "sizes": "128x128",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-144x144.png",
      "sizes": "144x144",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-152x152.png",
      "sizes": "152x152",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-384x384.png",
      "sizes": "384x384",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-512x512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
```

### 2. Service Worker實現
```javascript
// src/serviceWorker.js
const CACHE_NAME = 'gratitude-diary-v1';
const API_CACHE_NAME = 'gratitude-diary-api-v1';

const STATIC_ASSETS = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/manifest.json'
];

// 安裝事件
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(STATIC_ASSETS))
      .then(() => self.skipWaiting())
  );
});

// 激活事件
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME && cacheName !== API_CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => self.clients.claim())
  );
});

// 網路請求攔截
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // API請求策略：網路優先，緩存備用
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(
      fetch(request)
        .then((response) => {
          if (response.ok) {
            const responseClone = response.clone();
            caches.open(API_CACHE_NAME)
              .then((cache) => cache.put(request, responseClone));
          }
          return response;
        })
        .catch(() => {
          return caches.match(request);
        })
    );
    return;
  }

  // 靜態資源策略：緩存優先
  event.respondWith(
    caches.match(request)
      .then((response) => {
        return response || fetch(request);
      })
  );
});

// 推播通知
self.addEventListener('push', (event) => {
  const options = {
    body: event.data ? event.data.text() : '您有新的感恩提醒',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: '查看詳情',
        icon: '/icons/checkmark.png'
      },
      {
        action: 'close',
        title: '關閉',
        icon: '/icons/xmark.png'
      }
    ]
  };

  event.waitUntil(
    self.registration.showNotification('感恩日記', options)
  );
});
```

### 3. 離線數據同步
```javascript
// src/utils/offlineSync.js
import { openDB } from 'idb';

class OfflineSync {
  constructor() {
    this.dbName = 'gratitude-diary-offline';
    this.version = 1;
    this.db = null;
  }

  async init() {
    this.db = await openDB(this.dbName, this.version, {
      upgrade(db) {
        // 創建離線數據存儲
        if (!db.objectStoreNames.contains('entries')) {
          const entryStore = db.createObjectStore('entries', { keyPath: 'id' });
          entryStore.createIndex('userId', 'userId');
          entryStore.createIndex('createdAt', 'createdAt');
        }

        if (!db.objectStoreNames.contains('pendingActions')) {
          db.createObjectStore('pendingActions', { keyPath: 'id', autoIncrement: true });
        }
      }
    });
  }

  // 保存離線數據
  async saveOfflineEntry(entry) {
    const tx = this.db.transaction('entries', 'readwrite');
    await tx.objectStore('entries').put({
      ...entry,
      isOffline: true,
      lastModified: Date.now()
    });
  }

  // 添加待同步操作
  async addPendingAction(action) {
    const tx = this.db.transaction('pendingActions', 'readwrite');
    await tx.objectStore('pendingActions').add({
      ...action,
      timestamp: Date.now()
    });
  }

  // 同步離線數據
  async syncOfflineData() {
    if (!navigator.onLine) return;

    const tx = this.db.transaction(['entries', 'pendingActions'], 'readwrite');
    const pendingActions = await tx.objectStore('pendingActions').getAll();

    for (const action of pendingActions) {
      try {
        await this.executePendingAction(action);
        await tx.objectStore('pendingActions').delete(action.id);
      } catch (error) {
        console.error('同步失敗:', error);
      }
    }
  }

  async executePendingAction(action) {
    switch (action.type) {
      case 'CREATE_ENTRY':
        await apiClient.post('/entries', action.data);
        break;
      case 'UPDATE_ENTRY':
        await apiClient.put(`/entries/${action.entryId}`, action.data);
        break;
      case 'DELETE_ENTRY':
        await apiClient.delete(`/entries/${action.entryId}`);
        break;
    }
  }
}

export const offlineSync = new OfflineSync();
```

## iOS平台

### 1. Swift API客戶端
```swift
// APIClient.swift
import Foundation
import FirebaseAuth

class APIClient {
    static let shared = APIClient()
    private let baseURL = "https://asia-east1-gratitude-diary.cloudfunctions.net/api"
    
    private init() {}
    
    // 通用請求方法
    func request<T: Codable>(
        endpoint: String,
        method: HTTPMethod = .GET,
        body: Data? = nil,
        responseType: T.Type
    ) async throws -> T {
        guard let url = URL(string: "\(baseURL)\(endpoint)") else {
            throw APIError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // 添加認證token
        if let user = Auth.auth().currentUser {
            let token = try await user.getIDToken()
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        
        if let body = body {
            request.httpBody = body
        }
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.invalidResponse
        }
        
        guard 200...299 ~= httpResponse.statusCode else {
            throw APIError.serverError(httpResponse.statusCode)
        }
        
        return try JSONDecoder().decode(T.self, from: data)
    }
}

// HTTP方法枚舉
enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case DELETE = "DELETE"
}

// API錯誤類型
enum APIError: Error {
    case invalidURL
    case invalidResponse
    case serverError(Int)
    case decodingError
}
```

### 2. 數據模型
```swift
// Models.swift
import Foundation

// 用戶模型
struct User: Codable {
    let uid: String
    let email: String
    let displayName: String
    let avatar: String?
    let preferences: UserPreferences
    let stats: UserStats
    let createdAt: Date
    let updatedAt: Date
}

struct UserPreferences: Codable {
    let language: String
    let timezone: String
    let theme: String
    let privacy: PrivacySettings
    let notifications: NotificationSettings
}

// 日記條目模型
struct DiaryEntry: Codable, Identifiable {
    let id: String
    let userId: String
    let content: String
    let emotion: String
    let tags: [String]
    let media: [MediaAttachment]
    let privacy: String
    let interactions: Interactions
    let isAnonymous: Bool
    let createdAt: Date
    let updatedAt: Date
}

struct MediaAttachment: Codable {
    let type: String
    let url: String
    let thumbnail: String?
    let metadata: MediaMetadata?
}

struct Interactions: Codable {
    let likes: Int
    let comments: Int
    let shares: Int
}

// API響應模型
struct APIResponse<T: Codable>: Codable {
    let success: Bool
    let data: T?
    let error: APIErrorResponse?
    let message: String?
}

struct APIErrorResponse: Codable {
    let code: String
    let message: String
    let details: String?
}
```

### 3. 日記服務
```swift
// DiaryService.swift
import Foundation

class DiaryService {
    private let apiClient = APIClient.shared
    
    // 獲取日記列表
    func getEntries(emotion: String? = nil, page: Int = 1, limit: Int = 20) async throws -> [DiaryEntry] {
        var endpoint = "/entries?page=\(page)&limit=\(limit)"
        if let emotion = emotion {
            endpoint += "&emotion=\(emotion)"
        }
        
        let response: APIResponse<EntriesResponse> = try await apiClient.request(
            endpoint: endpoint,
            responseType: APIResponse<EntriesResponse>.self
        )
        
        guard let data = response.data else {
            throw APIError.invalidResponse
        }
        
        return data.entries
    }
    
    // 創建日記
    func createEntry(_ entry: CreateEntryRequest) async throws -> DiaryEntry {
        let body = try JSONEncoder().encode(entry)
        
        let response: APIResponse<EntryResponse> = try await apiClient.request(
            endpoint: "/entries",
            method: .POST,
            body: body,
            responseType: APIResponse<EntryResponse>.self
        )
        
        guard let data = response.data else {
            throw APIError.invalidResponse
        }
        
        return data.entry
    }
    
    // 點讚日記
    func likeEntry(_ entryId: String) async throws -> LikeResponse {
        let response: APIResponse<LikeResponse> = try await apiClient.request(
            endpoint: "/entries/\(entryId)/like",
            method: .POST,
            responseType: APIResponse<LikeResponse>.self
        )
        
        guard let data = response.data else {
            throw APIError.invalidResponse
        }
        
        return data
    }
}

// 請求/響應模型
struct CreateEntryRequest: Codable {
    let content: String
    let emotion: String
    let tags: [String]
    let privacy: String
    let isAnonymous: Bool
}

struct EntriesResponse: Codable {
    let entries: [DiaryEntry]
    let pagination: Pagination
}

struct EntryResponse: Codable {
    let entry: DiaryEntry
}

struct LikeResponse: Codable {
    let liked: Bool
    let likeCount: Int
}

struct Pagination: Codable {
    let page: Int
    let limit: Int
    let total: Int
    let totalPages: Int
}
```

## Android平台

### 1. Kotlin API客戶端
```kotlin
// ApiClient.kt
import com.google.firebase.auth.FirebaseAuth
import kotlinx.coroutines.tasks.await
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import com.google.gson.Gson
import java.io.IOException

class ApiClient {
    companion object {
        private const val BASE_URL = "https://asia-east1-gratitude-diary.cloudfunctions.net/api"
        val instance = ApiClient()
    }
    
    private val client = OkHttpClient()
    private val gson = Gson()
    
    suspend inline fun <reified T> request(
        endpoint: String,
        method: String = "GET",
        body: Any? = null
    ): ApiResponse<T> {
        val url = "$BASE_URL$endpoint"
        
        val requestBuilder = Request.Builder().url(url)
        
        // 添加認證token
        FirebaseAuth.getInstance().currentUser?.let { user ->
            val token = user.getIdToken(false).await().token
            requestBuilder.addHeader("Authorization", "Bearer $token")
        }
        
        // 設置請求體
        body?.let {
            val json = gson.toJson(it)
            val requestBody = json.toRequestBody("application/json".toMediaType())
            requestBuilder.method(method, requestBody)
        }
        
        val request = requestBuilder.build()
        
        return try {
            val response = client.newCall(request).execute()
            val responseBody = response.body?.string()
            
            if (response.isSuccessful && responseBody != null) {
                gson.fromJson(responseBody, ApiResponse::class.java) as ApiResponse<T>
            } else {
                ApiResponse(
                    success = false,
                    error = ApiError("HTTP_ERROR", "請求失敗: ${response.code}")
                )
            }
        } catch (e: IOException) {
            ApiResponse(
                success = false,
                error = ApiError("NETWORK_ERROR", "網路錯誤: ${e.message}")
            )
        }
    }
}

// 數據類
data class ApiResponse<T>(
    val success: Boolean,
    val data: T? = null,
    val error: ApiError? = null,
    val message: String? = null
)

data class ApiError(
    val code: String,
    val message: String,
    val details: String? = null
)
```

### 2. 數據模型
```kotlin
// Models.kt
import com.google.gson.annotations.SerializedName
import java.util.Date

data class DiaryEntry(
    val id: String,
    val userId: String,
    val content: String,
    val emotion: String,
    val tags: List<String>,
    val media: List<MediaAttachment>,
    val privacy: String,
    val interactions: Interactions,
    val isAnonymous: Boolean,
    val createdAt: Date,
    val updatedAt: Date
)

data class MediaAttachment(
    val type: String,
    val url: String,
    val thumbnail: String?,
    val metadata: MediaMetadata?
)

data class Interactions(
    val likes: Int,
    val comments: Int,
    val shares: Int
)

data class User(
    val uid: String,
    val email: String,
    val displayName: String,
    val avatar: String?,
    val preferences: UserPreferences,
    val stats: UserStats,
    val createdAt: Date,
    val updatedAt: Date
)

data class CreateEntryRequest(
    val content: String,
    val emotion: String,
    val tags: List<String>,
    val privacy: String,
    val isAnonymous: Boolean
)
```

### 3. Repository模式
```kotlin
// DiaryRepository.kt
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

class DiaryRepository {
    private val apiClient = ApiClient.instance
    
    fun getEntries(
        emotion: String? = null,
        page: Int = 1,
        limit: Int = 20
    ): Flow<Result<List<DiaryEntry>>> = flow {
        try {
            var endpoint = "/entries?page=$page&limit=$limit"
            emotion?.let { endpoint += "&emotion=$it" }
            
            val response = apiClient.request<EntriesResponse>(endpoint)
            
            if (response.success && response.data != null) {
                emit(Result.success(response.data.entries))
            } else {
                emit(Result.failure(Exception(response.error?.message ?: "未知錯誤")))
            }
        } catch (e: Exception) {
            emit(Result.failure(e))
        }
    }
    
    suspend fun createEntry(request: CreateEntryRequest): Result<DiaryEntry> {
        return try {
            val response = apiClient.request<EntryResponse>(
                endpoint = "/entries",
                method = "POST",
                body = request
            )
            
            if (response.success && response.data != null) {
                Result.success(response.data.entry)
            } else {
                Result.failure(Exception(response.error?.message ?: "創建失敗"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun likeEntry(entryId: String): Result<LikeResponse> {
        return try {
            val response = apiClient.request<LikeResponse>(
                endpoint = "/entries/$entryId/like",
                method = "POST"
            )
            
            if (response.success && response.data != null) {
                Result.success(response.data)
            } else {
                Result.failure(Exception(response.error?.message ?: "點讚失敗"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

data class EntriesResponse(
    val entries: List<DiaryEntry>,
    val pagination: Pagination
)

data class EntryResponse(
    val entry: DiaryEntry
)

data class LikeResponse(
    val liked: Boolean,
    val likeCount: Int
)
```

## 桌面應用 (Electron)

### 1. Electron主進程
```javascript
// main.js
const { app, BrowserWindow, Menu, Tray, nativeImage } = require('electron');
const path = require('path');
const isDev = require('electron-is-dev');

let mainWindow;
let tray;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'assets/icon.png'),
    show: false
  });

  const startUrl = isDev
    ? 'http://localhost:3000'
    : `file://${path.join(__dirname, '../build/index.html')}`;

  mainWindow.loadURL(startUrl);

  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // 創建系統托盤
  createTray();
}

function createTray() {
  const trayIcon = nativeImage.createFromPath(
    path.join(__dirname, 'assets/tray-icon.png')
  );

  tray = new Tray(trayIcon);

  const contextMenu = Menu.buildFromTemplate([
    {
      label: '顯示應用',
      click: () => {
        mainWindow.show();
      }
    },
    {
      label: '新增日記',
      click: () => {
        mainWindow.show();
        mainWindow.webContents.send('navigate-to', '/app/create');
      }
    },
    { type: 'separator' },
    {
      label: '退出',
      click: () => {
        app.quit();
      }
    }
  ]);

  tray.setContextMenu(contextMenu);
  tray.setToolTip('感恩日記');
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
```

### 2. 預載腳本
```javascript
// preload.js
const { contextBridge, ipcRenderer } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  // 導航控制
  navigateTo: (route) => {
    ipcRenderer.send('navigate-to', route);
  },

  // 通知
  showNotification: (title, body) => {
    new Notification(title, { body });
  },

  // 文件操作
  saveFile: (data, filename) => {
    ipcRenderer.invoke('save-file', data, filename);
  },

  openFile: () => {
    return ipcRenderer.invoke('open-file');
  },

  // 系統信息
  getPlatform: () => {
    return process.platform;
  }
});

// 監聽主進程消息
ipcRenderer.on('navigate-to', (event, route) => {
  window.postMessage({ type: 'NAVIGATE_TO', route }, '*');
});
```

## 統一認證策略

### 1. 多平台認證配置
```javascript
// 認證配置統一管理
const authConfig = {
  web: {
    provider: 'firebase',
    config: {
      apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
      authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
      // ... 其他配置
    }
  },
  ios: {
    provider: 'firebase',
    config: {
      plistPath: 'GoogleService-Info.plist'
    }
  },
  android: {
    provider: 'firebase',
    config: {
      jsonPath: 'google-services.json'
    }
  },
  desktop: {
    provider: 'firebase',
    config: {
      // 與web相同配置
    }
  }
};
```

### 2. Token同步機制
```javascript
// Token同步服務
class TokenSyncService {
  constructor(platform) {
    this.platform = platform;
    this.tokenRefreshInterval = null;
  }

  async startTokenSync() {
    // 每50分鐘刷新token
    this.tokenRefreshInterval = setInterval(async () => {
      try {
        await this.refreshToken();
      } catch (error) {
        console.error('Token刷新失敗:', error);
      }
    }, 50 * 60 * 1000);
  }

  async refreshToken() {
    const user = firebase.auth().currentUser;
    if (user) {
      const token = await user.getIdToken(true);
      await this.updateApiClientToken(token);
      await this.syncTokenAcrossPlatforms(token);
    }
  }

  async syncTokenAcrossPlatforms(token) {
    // 根據平台同步token
    switch (this.platform) {
      case 'web':
        localStorage.setItem('authToken', token);
        break;
      case 'ios':
        // 使用Keychain存儲
        await this.saveToKeychain(token);
        break;
      case 'android':
        // 使用SharedPreferences存儲
        await this.saveToSharedPreferences(token);
        break;
      case 'desktop':
        // 使用electron-store存儲
        await this.saveToElectronStore(token);
        break;
    }
  }
}
```

## 數據同步策略

### 1. 衝突解決機制
```javascript
// 數據衝突解決
class ConflictResolver {
  resolveEntryConflict(localEntry, serverEntry) {
    // 基於時間戳的衝突解決
    if (localEntry.updatedAt > serverEntry.updatedAt) {
      return {
        resolution: 'use_local',
        entry: localEntry,
        reason: 'Local version is newer'
      };
    } else if (localEntry.updatedAt < serverEntry.updatedAt) {
      return {
        resolution: 'use_server',
        entry: serverEntry,
        reason: 'Server version is newer'
      };
    } else {
      // 時間戳相同，比較內容長度
      if (localEntry.content.length > serverEntry.content.length) {
        return {
          resolution: 'use_local',
          entry: localEntry,
          reason: 'Local version has more content'
        };
      } else {
        return {
          resolution: 'use_server',
          entry: serverEntry,
          reason: 'Server version preferred'
        };
      }
    }
  }

  async mergeUserPreferences(localPrefs, serverPrefs) {
    // 合併用戶偏好設定
    return {
      ...serverPrefs,
      ...localPrefs,
      // 特殊處理某些字段
      notifications: {
        ...serverPrefs.notifications,
        ...localPrefs.notifications
      },
      lastSyncTime: Date.now()
    };
  }
}
```

### 2. 增量同步
```javascript
// 增量同步服務
class IncrementalSyncService {
  async syncChanges(lastSyncTime) {
    try {
      // 獲取服務器端變更
      const serverChanges = await this.getServerChanges(lastSyncTime);

      // 獲取本地變更
      const localChanges = await this.getLocalChanges(lastSyncTime);

      // 解決衝突
      const resolvedChanges = await this.resolveConflicts(
        localChanges,
        serverChanges
      );

      // 應用變更
      await this.applyChanges(resolvedChanges);

      // 更新同步時間戳
      await this.updateLastSyncTime();

      return { success: true, changes: resolvedChanges };
    } catch (error) {
      console.error('增量同步失敗:', error);
      return { success: false, error };
    }
  }

  async getServerChanges(since) {
    const response = await apiClient.get('/sync/changes', {
      params: { since }
    });
    return response.data.changes;
  }

  async getLocalChanges(since) {
    // 從本地數據庫獲取變更
    return await localDB.getChangesSince(since);
  }
}
```

## 推播通知統一管理

### 1. 跨平台推播配置
```javascript
// 推播通知管理器
class NotificationManager {
  constructor(platform) {
    this.platform = platform;
    this.fcmToken = null;
  }

  async initialize() {
    switch (this.platform) {
      case 'web':
        await this.initializeWebNotifications();
        break;
      case 'ios':
        await this.initializeIOSNotifications();
        break;
      case 'android':
        await this.initializeAndroidNotifications();
        break;
      case 'desktop':
        await this.initializeDesktopNotifications();
        break;
    }
  }

  async initializeWebNotifications() {
    if ('serviceWorker' in navigator && 'PushManager' in window) {
      const registration = await navigator.serviceWorker.ready;
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(vapidPublicKey)
      });

      await this.registerSubscription(subscription);
    }
  }

  async sendCrossPlatformNotification(userId, notification) {
    // 獲取用戶的所有設備token
    const userTokens = await this.getUserDeviceTokens(userId);

    // 發送到所有設備
    const promises = userTokens.map(token =>
      this.sendToDevice(token, notification)
    );

    return await Promise.allSettled(promises);
  }
}
```

## 性能優化策略

### 1. 數據預載和緩存
```javascript
// 智能預載服務
class PreloadService {
  constructor(platform) {
    this.platform = platform;
    this.cache = new Map();
  }

  async preloadUserData(userId) {
    const preloadTasks = [
      this.preloadRecentEntries(userId),
      this.preloadUserStats(userId),
      this.preloadUserPreferences(userId)
    ];

    const results = await Promise.allSettled(preloadTasks);

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        this.cache.set(this.getPreloadKey(index), result.value);
      }
    });
  }

  async preloadRecentEntries(userId) {
    const entries = await apiClient.get(`/users/${userId}/entries/recent`);
    return entries.data;
  }

  getCachedData(key) {
    return this.cache.get(key);
  }
}
```

### 2. 圖片優化
```javascript
// 圖片優化服務
class ImageOptimizationService {
  async optimizeForPlatform(imageUrl, platform) {
    const optimizations = {
      web: { format: 'webp', quality: 80, sizes: [400, 800, 1200] },
      ios: { format: 'heic', quality: 85, sizes: [375, 750, 1125] },
      android: { format: 'webp', quality: 80, sizes: [360, 720, 1080] },
      desktop: { format: 'webp', quality: 90, sizes: [600, 1200, 1800] }
    };

    const config = optimizations[platform];
    return await this.generateOptimizedImages(imageUrl, config);
  }

  async generateOptimizedImages(imageUrl, config) {
    const optimizedImages = {};

    for (const size of config.sizes) {
      const optimizedUrl = await this.resizeAndCompress(
        imageUrl,
        size,
        config.format,
        config.quality
      );
      optimizedImages[`${size}w`] = optimizedUrl;
    }

    return optimizedImages;
  }
}
```

這個多平台支援指南提供了完整的跨平台架構設計，確保感恩日記應用能夠在Web、iOS、Android和桌面等多個平台上提供一致的用戶體驗和功能。
```
