import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, Typography, Button, IconButton } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';

const onboardingData = [
  {
    id: 1,
    title: '開始你的感恩之旅',
    description: '記錄生活中的美好時刻\n培養感恩的心，提升幸福感',
    emoji: '🌟',
    color: '#F59E0B'
  },
  {
    id: 2,
    title: '發現感恩的力量',
    description: '與他人分享你的感恩故事\n在社群中找到情感共鳴',
    emoji: '💝',
    color: '#C084FC'
  },
  {
    id: 3,
    title: '保持專注與平衡',
    description: '工作專注，適時休息\n感謝身心的協調配合',
    emoji: '🎯',
    color: '#10B981'
  }
];

const OnboardingScreen = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const navigate = useNavigate();

  const handleNext = () => {
    if (currentStep < onboardingData.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      navigate('/app');
    }
  };

  const handlePrev = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSkip = () => {
    navigate('/app');
  };

  const currentData = onboardingData[currentStep];

  return (
    <Box
      sx={{
        height: '100vh',
        width: '100vw',
        background: 'linear-gradient(135deg, #F9FAFB 0%, #EDE9FE 100%)',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        overflow: 'hidden',
        maxWidth: '100%',
        margin: '0 auto',
      }}
    >
      {/* 跳過按鈕 */}
      <Box sx={{ position: 'absolute', top: 16, right: 16, zIndex: 10 }}>
        <Button
          onClick={handleSkip}
          sx={{ color: 'text.secondary', fontWeight: 500 }}
        >
          跳過
        </Button>
      </Box>

      {/* 主要內容 */}
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          px: 4,
          textAlign: 'center',
          maxWidth: '600px',
          width: '100%',
          margin: '0 auto',
        }}
      >
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.3 }}
            style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}
          >
            {/* 插圖區域 */}
            <Box
              sx={{
                width: 200,
                height: 200,
                borderRadius: '50%',
                background: `linear-gradient(45deg, ${currentData.color}, ${currentData.color}40)`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '4rem',
                mb: 4,
                boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
              }}
            >
              {currentData.emoji}
            </Box>

            {/* 標題 */}
            <Typography
              variant="h2"
              sx={{
                mb: 3,
                color: 'text.primary',
                fontWeight: 700,
              }}
            >
              {currentData.title}
            </Typography>

            {/* 描述 */}
            <Typography
              variant="body1"
              sx={{
                color: 'text.secondary',
                lineHeight: 1.6,
                maxWidth: 300,
                whiteSpace: 'pre-line',
              }}
            >
              {currentData.description}
            </Typography>
          </motion.div>
        </AnimatePresence>
      </Box>

      {/* 底部導航 */}
      <Box
        sx={{
          p: 4,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: 3,
          maxWidth: '600px',
          width: '100%',
          margin: '0 auto',
        }}
      >
        {/* 進度指示器 */}
        <Box sx={{ display: 'flex', gap: 1 }}>
          {onboardingData.map((_, index) => (
            <Box
              key={index}
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                bgcolor: index === currentStep ? 'primary.main' : 'grey.300',
                transition: 'all 0.3s ease',
              }}
            />
          ))}
        </Box>

        {/* 導航按鈕 */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '100%',
            maxWidth: 300,
          }}
        >
          <IconButton
            onClick={handlePrev}
            disabled={currentStep === 0}
            sx={{
              opacity: currentStep === 0 ? 0 : 1,
              transition: 'opacity 0.3s ease',
            }}
          >
            <ArrowBackIcon />
          </IconButton>

          <Button
            variant="contained"
            onClick={handleNext}
            endIcon={<ArrowForwardIcon />}
            sx={{
              px: 4,
              py: 1.5,
              bgcolor: 'primary.main',
              '&:hover': {
                bgcolor: 'primary.dark',
              },
            }}
          >
            {currentStep === onboardingData.length - 1 ? '開始使用' : '下一步'}
          </Button>

          <Box sx={{ width: 40 }} /> {/* 佔位符保持對稱 */}
        </Box>
      </Box>
    </Box>
  );
};

export default OnboardingScreen;
