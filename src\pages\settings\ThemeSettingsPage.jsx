import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  AppBar,
  Toolbar,
  IconButton,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Switch,
  Button,
  Slider,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  FormLabel,
  Snackbar,
  Alert,
  Divider,
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import DarkModeIcon from '@mui/icons-material/DarkMode';
import LightModeIcon from '@mui/icons-material/LightMode';
import AutoModeIcon from '@mui/icons-material/AutoMode';
import TextFieldsIcon from '@mui/icons-material/TextFields';
import PaletteIcon from '@mui/icons-material/Palette';
import { motion } from 'framer-motion';

const ThemeSettingsPage = () => {
  const navigate = useNavigate();
  const [settings, setSettings] = useState({
    darkMode: false,
    autoTheme: false,
    fontSize: 16,
    colorTheme: 'purple', // purple, blue, green, pink
    highContrast: false,
    reducedMotion: false,
  });
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  const handleBack = () => {
    navigate('/app/settings');
  };

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value,
    }));
    setShowSuccessMessage(true);
  };

  const colorThemes = [
    {
      id: 'purple',
      name: '優雅紫色',
      primary: '#6B46C1',
      secondary: '#F59E0B',
      description: '經典的紫色配金色主題',
    },
    {
      id: 'blue',
      name: '寧靜藍色',
      primary: '#2563EB',
      secondary: '#F59E0B',
      description: '平靜的藍色主題',
    },
    {
      id: 'green',
      name: '自然綠色',
      primary: '#059669',
      secondary: '#F59E0B',
      description: '清新的綠色主題',
    },
    {
      id: 'pink',
      name: '溫暖粉色',
      primary: '#EC4899',
      secondary: '#F59E0B',
      description: '溫馨的粉色主題',
    },
  ];

  const fontSizeLabels = {
    12: '極小',
    14: '小',
    16: '標準',
    18: '大',
    20: '極大',
  };

  return (
    <Box sx={{ 
      bgcolor: 'background.default', 
      minHeight: '100vh',
      width: '100%',
      maxWidth: '100vw',
      margin: '0 auto',
    }}>
      {/* 頂部導航欄 */}
      <AppBar
        position="sticky"
        elevation={0}
        sx={{
          bgcolor: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(0,0,0,0.1)',
        }}
      >
        <Toolbar sx={{ maxWidth: '600px', width: '100%', margin: '0 auto' }}>
          <IconButton onClick={handleBack} sx={{ mr: 2 }}>
            <ArrowBackIcon sx={{ color: 'text.primary' }} />
          </IconButton>
          <Typography
            variant="h3"
            sx={{
              flex: 1,
              color: 'text.primary',
              fontWeight: 600,
            }}
          >
            主題設定
          </Typography>
        </Toolbar>
      </AppBar>

      <Box sx={{ 
        p: 2,
        maxWidth: '600px',
        width: '100%',
        margin: '0 auto',
      }}>
        {/* 深色模式設定 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            外觀模式
          </Typography>
          <Card sx={{ mb: 3, borderRadius: 4 }}>
            <CardContent>
              <FormControl component="fieldset">
                <RadioGroup
                  value={settings.autoTheme ? 'auto' : (settings.darkMode ? 'dark' : 'light')}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (value === 'auto') {
                      handleSettingChange('autoTheme', true);
                    } else {
                      handleSettingChange('autoTheme', false);
                      handleSettingChange('darkMode', value === 'dark');
                    }
                  }}
                >
                  <FormControlLabel
                    value="light"
                    control={<Radio />}
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <LightModeIcon />
                        <Box>
                          <Typography variant="body1">淺色模式</Typography>
                          <Typography variant="body2" color="text.secondary">
                            明亮清新的界面
                          </Typography>
                        </Box>
                      </Box>
                    }
                  />
                  <FormControlLabel
                    value="dark"
                    control={<Radio />}
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <DarkModeIcon />
                        <Box>
                          <Typography variant="body1">深色模式</Typography>
                          <Typography variant="body2" color="text.secondary">
                            護眼的深色界面
                          </Typography>
                        </Box>
                      </Box>
                    }
                  />
                  <FormControlLabel
                    value="auto"
                    control={<Radio />}
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <AutoModeIcon />
                        <Box>
                          <Typography variant="body1">自動切換</Typography>
                          <Typography variant="body2" color="text.secondary">
                            跟隨系統設定
                          </Typography>
                        </Box>
                      </Box>
                    }
                  />
                </RadioGroup>
              </FormControl>
            </CardContent>
          </Card>
        </motion.div>

        {/* 色彩主題 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            色彩主題
          </Typography>
          <Card sx={{ mb: 3, borderRadius: 4 }}>
            <CardContent>
              <Box sx={{ display: 'grid', gap: 2 }}>
                {colorThemes.map((theme) => (
                  <Box
                    key={theme.id}
                    onClick={() => handleSettingChange('colorTheme', theme.id)}
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      border: '2px solid',
                      borderColor: settings.colorTheme === theme.id ? theme.primary : 'divider',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        borderColor: theme.primary,
                        bgcolor: `${theme.primary}10`,
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Box
                          sx={{
                            width: 24,
                            height: 24,
                            borderRadius: '50%',
                            bgcolor: theme.primary,
                          }}
                        />
                        <Box
                          sx={{
                            width: 24,
                            height: 24,
                            borderRadius: '50%',
                            bgcolor: theme.secondary,
                          }}
                        />
                      </Box>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          {theme.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {theme.description}
                        </Typography>
                      </Box>
                      <Radio
                        checked={settings.colorTheme === theme.id}
                        sx={{ color: theme.primary }}
                      />
                    </Box>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </motion.div>

        {/* 字體大小 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            字體大小
          </Typography>
          <Card sx={{ mb: 3, borderRadius: 4 }}>
            <CardContent>
              <Box sx={{ px: 2 }}>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  當前大小：{fontSizeLabels[settings.fontSize]} ({settings.fontSize}px)
                </Typography>
                <Slider
                  value={settings.fontSize}
                  onChange={(e, value) => handleSettingChange('fontSize', value)}
                  min={12}
                  max={20}
                  step={2}
                  marks={Object.keys(fontSizeLabels).map(size => ({
                    value: parseInt(size),
                    label: fontSizeLabels[size],
                  }))}
                  valueLabelDisplay="auto"
                  sx={{ mb: 2 }}
                />
                <Box sx={{ 
                  p: 2, 
                  bgcolor: 'background.paper', 
                  borderRadius: 2,
                  border: '1px solid',
                  borderColor: 'divider',
                }}>
                  <Typography 
                    variant="body1" 
                    sx={{ fontSize: `${settings.fontSize}px` }}
                  >
                    這是字體大小預覽文字。感恩每一天的美好時光！
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </motion.div>

        {/* 無障礙設定 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            無障礙設定
          </Typography>
          <Card sx={{ borderRadius: 4 }}>
            <List sx={{ py: 0 }}>
              <ListItem sx={{ py: 2 }}>
                <ListItemText
                  primary="高對比度"
                  secondary="增強文字和背景的對比度"
                />
                <Switch
                  checked={settings.highContrast}
                  onChange={(e) => handleSettingChange('highContrast', e.target.checked)}
                  color="primary"
                />
              </ListItem>
              <Divider variant="inset" component="li" />
              <ListItem sx={{ py: 2 }}>
                <ListItemText
                  primary="減少動畫"
                  secondary="減少界面動畫效果"
                />
                <Switch
                  checked={settings.reducedMotion}
                  onChange={(e) => handleSettingChange('reducedMotion', e.target.checked)}
                  color="primary"
                />
              </ListItem>
            </List>
          </Card>
        </motion.div>
      </Box>

      {/* 成功提示 */}
      <Snackbar
        open={showSuccessMessage}
        autoHideDuration={2000}
        onClose={() => setShowSuccessMessage(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert 
          onClose={() => setShowSuccessMessage(false)} 
          severity="success"
          sx={{ width: '100%' }}
        >
          主題設定已更新！
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ThemeSettingsPage;
