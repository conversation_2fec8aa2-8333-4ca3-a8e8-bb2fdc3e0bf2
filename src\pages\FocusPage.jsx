import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  AppBar,
  <PERSON><PERSON><PERSON>,
  IconButton,
  Button,
  Card,
  CardContent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Slider,
  Switch,
  FormControlLabel,
} from '@mui/material';
import SettingsIcon from '@mui/icons-material/Settings';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';
import RefreshIcon from '@mui/icons-material/Refresh';
import { motion } from 'framer-motion';

const FocusPage = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [timeLeft, setTimeLeft] = useState(25 * 60); // 25分鐘，以秒為單位
  const [isWorkTime, setIsWorkTime] = useState(true);
  const [workDuration, setWorkDuration] = useState(25); // 分鐘
  const [breakDuration, setBreakDuration] = useState(10); // 分鐘
  const [showSettings, setShowSettings] = useState(false);
  const [showBreakDialog, setShowBreakDialog] = useState(false);
  const [gratitudeMessage, setGratitudeMessage] = useState(
    '謝謝我美好的身體及智慧共同專心的完成了{$workTime}分鐘的工作，現在我們休息{$breakTime}分鐘'
  );
  const [customGratitudeMessage, setCustomGratitudeMessage] = useState('');
  const [todayStats, setTodayStats] = useState({
    focusSessions: 3,
    totalMinutes: 90,
  });

  const intervalRef = useRef(null);

  useEffect(() => {
    if (isRunning && timeLeft > 0) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(prev => prev - 1);
      }, 1000);
    } else if (timeLeft === 0) {
      handleTimerComplete();
    } else {
      clearInterval(intervalRef.current);
    }

    return () => clearInterval(intervalRef.current);
  }, [isRunning, timeLeft]);

  const handleTimerComplete = () => {
    setIsRunning(false);
    if (isWorkTime) {
      // 工作時間結束，顯示休息提醒
      setShowBreakDialog(true);
      setTodayStats(prev => ({
        focusSessions: prev.focusSessions + 1,
        totalMinutes: prev.totalMinutes + workDuration,
      }));
    } else {
      // 休息時間結束，切換回工作模式
      setIsWorkTime(true);
      setTimeLeft(workDuration * 60);
    }
  };

  const handleStartPause = () => {
    setIsRunning(!isRunning);
  };

  const handleReset = () => {
    setIsRunning(false);
    setTimeLeft(isWorkTime ? workDuration * 60 : breakDuration * 60);
  };

  const handleStartBreak = () => {
    setShowBreakDialog(false);
    setIsWorkTime(false);
    setTimeLeft(breakDuration * 60);
    setIsRunning(true);
  };

  const handleSkipBreak = () => {
    setShowBreakDialog(false);
    setTimeLeft(workDuration * 60);
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getProgress = () => {
    const totalTime = isWorkTime ? workDuration * 60 : breakDuration * 60;
    return ((totalTime - timeLeft) / totalTime) * 100;
  };

  const getCurrentGratitudeMessage = () => {
    const message = customGratitudeMessage || gratitudeMessage;
    return message
      .replace('{$workTime}', workDuration.toString())
      .replace('{$breakTime}', breakDuration.toString())
      .replace('{$30}', workDuration.toString())
      .replace('{$10}', breakDuration.toString());
  };

  return (
    <Box sx={{
      bgcolor: 'background.default',
      minHeight: '100vh',
      width: '100%',
      maxWidth: '100vw',
      margin: '0 auto',
    }}>
      {/* 頂部導航欄 */}
      <AppBar
        position="sticky"
        elevation={0}
        sx={{
          bgcolor: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(0,0,0,0.1)',
        }}
      >
        <Toolbar sx={{ maxWidth: '600px', width: '100%', margin: '0 auto' }}>
          <Typography
            variant="h3"
            sx={{
              flex: 1,
              color: 'text.primary',
              fontWeight: 600,
              textAlign: 'center',
            }}
          >
            專注工作
          </Typography>
          <IconButton onClick={() => setShowSettings(true)}>
            <SettingsIcon sx={{ color: 'text.primary' }} />
          </IconButton>
        </Toolbar>
      </AppBar>

      <Box sx={{
        p: 3,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        maxWidth: '600px',
        width: '100%',
        margin: '0 auto',
      }}>
        {/* 計時器區域 */}
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Box
            sx={{
              position: 'relative',
              width: 280,
              height: 280,
              mb: 4,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {/* 進度圓環 */}
            <svg
              width="280"
              height="280"
              style={{ position: 'absolute', transform: 'rotate(-90deg)' }}
            >
              {/* 背景圓環 */}
              <circle
                cx="140"
                cy="140"
                r="130"
                fill="none"
                stroke="#EDE9FE"
                strokeWidth="8"
              />
              {/* 進度圓環 */}
              <circle
                cx="140"
                cy="140"
                r="130"
                fill="none"
                stroke="url(#gradient)"
                strokeWidth="8"
                strokeLinecap="round"
                strokeDasharray={`${2 * Math.PI * 130}`}
                strokeDashoffset={`${2 * Math.PI * 130 * (1 - getProgress() / 100)}`}
                style={{ transition: 'stroke-dashoffset 1s ease' }}
              />
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor="#6B46C1" />
                  <stop offset="100%" stopColor="#A78BFA" />
                </linearGradient>
              </defs>
            </svg>

            {/* 時間顯示 */}
            <Box sx={{ textAlign: 'center', zIndex: 1 }}>
              <Typography
                variant="h1"
                sx={{
                  fontSize: '3rem',
                  fontWeight: 700,
                  color: 'text.primary',
                  mb: 1,
                }}
              >
                {formatTime(timeLeft)}
              </Typography>
              <Typography
                variant="h6"
                sx={{
                  color: 'text.secondary',
                  fontWeight: 500,
                }}
              >
                {isWorkTime ? '專注工作中' : '休息時間'}
              </Typography>
            </Box>
          </Box>
        </motion.div>

        {/* 控制按鈕 */}
        <Box sx={{ display: 'flex', gap: 2, mb: 4 }}>
          <Button
            variant="contained"
            onClick={handleStartPause}
            sx={{
              width: 80,
              height: 80,
              borderRadius: '50%',
              bgcolor: 'secondary.main',
              color: 'white',
              fontSize: '2rem',
              '&:hover': {
                bgcolor: 'secondary.dark',
              },
            }}
          >
            {isRunning ? <PauseIcon fontSize="large" /> : <PlayArrowIcon fontSize="large" />}
          </Button>
          <Button
            variant="outlined"
            onClick={handleReset}
            sx={{
              width: 60,
              height: 60,
              borderRadius: '50%',
              borderColor: 'text.secondary',
              color: 'text.secondary',
              '&:hover': {
                borderColor: 'primary.main',
                color: 'primary.main',
              },
            }}
          >
            <RefreshIcon />
          </Button>
        </Box>

        {/* 今日統計 */}
        <Card sx={{ width: '100%', maxWidth: 400 }}>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              📊 今日統計
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                  {todayStats.focusSessions}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  專注次數
                </Typography>
              </Box>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" sx={{ fontWeight: 700, color: 'secondary.main' }}>
                  {todayStats.totalMinutes}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  總計分鐘
                </Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>
      </Box>

      {/* 設定對話框 */}
      <Dialog open={showSettings} onClose={() => setShowSettings(false)} maxWidth="sm" fullWidth>
        <DialogTitle>專注設定</DialogTitle>
        <DialogContent>
          <Box sx={{ py: 2 }}>
            <Typography gutterBottom>工作時間（分鐘）</Typography>
            <Slider
              value={workDuration}
              onChange={(e, value) => setWorkDuration(value)}
              min={5}
              max={60}
              step={5}
              marks
              valueLabelDisplay="auto"
              sx={{ mb: 3 }}
            />

            <Typography gutterBottom>休息時間（分鐘）</Typography>
            <Slider
              value={breakDuration}
              onChange={(e, value) => setBreakDuration(value)}
              min={5}
              max={30}
              step={5}
              marks
              valueLabelDisplay="auto"
              sx={{ mb: 3 }}
            />

            <Typography gutterBottom>自定義感謝詞</Typography>
            <TextField
              multiline
              rows={3}
              fullWidth
              placeholder={gratitudeMessage}
              value={customGratitudeMessage}
              onChange={(e) => setCustomGratitudeMessage(e.target.value)}
              helperText="使用 {$workTime} 和 {$breakTime} 作為時間變數"
              sx={{ mb: 2 }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowSettings(false)}>取消</Button>
          <Button
            onClick={() => {
              setTimeLeft(workDuration * 60);
              setShowSettings(false);
            }}
            variant="contained"
          >
            保存
          </Button>
        </DialogActions>
      </Dialog>

      {/* 休息提醒對話框 */}
      <Dialog
        open={showBreakDialog}
        onClose={() => {}}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 4,
            border: '2px solid',
            borderColor: 'secondary.main',
          },
        }}
      >
        <DialogContent sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h2" sx={{ mb: 2 }}>
            🎉
          </Typography>
          <Typography
            variant="h6"
            sx={{
              mb: 3,
              lineHeight: 1.6,
              color: 'text.primary',
              whiteSpace: 'pre-line',
            }}
          >
            {getCurrentGratitudeMessage()}
          </Typography>
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
              🧘‍♀️ 建議活動：
            </Typography>
            <Box sx={{ textAlign: 'left', maxWidth: 200, mx: 'auto' }}>
              <Typography variant="body2" sx={{ mb: 1 }}>
                • 深呼吸 5次
              </Typography>
              <Typography variant="body2" sx={{ mb: 1 }}>
                • 眼部運動
              </Typography>
              <Typography variant="body2">
                • 簡單伸展
              </Typography>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions sx={{ justifyContent: 'center', pb: 3 }}>
          <Button onClick={handleSkipBreak} sx={{ mr: 2 }}>
            跳過
          </Button>
          <Button variant="contained" onClick={handleStartBreak}>
            開始休息
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FocusPage;
