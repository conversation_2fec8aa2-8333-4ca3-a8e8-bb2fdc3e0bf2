import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  AppBar,
  Toolbar,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Switch,
  Card,
  Divider,
  RadioGroup,
  FormControlLabel,
  Radio,
  Alert,
  Chip,
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import DarkModeIcon from '@mui/icons-material/DarkMode';
import LightModeIcon from '@mui/icons-material/LightMode';
import AutoModeIcon from '@mui/icons-material/AutoMode';
import ScheduleIcon from '@mui/icons-material/Schedule';
import BrightnessAutoIcon from '@mui/icons-material/BrightnessAuto';
import { motion } from 'framer-motion';

const DarkModeSettingsPage = () => {
  const navigate = useNavigate();
  const [settings, setSettings] = useState({
    darkMode: false,
    autoMode: false,
    scheduleMode: false,
    followSystem: true,
    startTime: '20:00',
    endTime: '07:00',
  });
  const [themeMode, setThemeMode] = useState('system'); // 'light', 'dark', 'system', 'schedule'

  const handleBack = () => {
    navigate('/app/settings');
  };

  const handleSettingChange = (key) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const handleThemeModeChange = (event) => {
    const newMode = event.target.value;
    setThemeMode(newMode);
    
    // 根據選擇更新相關設定
    setSettings(prev => ({
      ...prev,
      darkMode: newMode === 'dark',
      autoMode: newMode === 'system',
      scheduleMode: newMode === 'schedule',
      followSystem: newMode === 'system',
    }));
  };

  const themeOptions = [
    {
      value: 'light',
      label: '淺色模式',
      description: '始終使用淺色主題',
      icon: <LightModeIcon />,
    },
    {
      value: 'dark',
      label: '深色模式',
      description: '始終使用深色主題',
      icon: <DarkModeIcon />,
    },
    {
      value: 'system',
      label: '跟隨系統',
      description: '根據系統設定自動切換',
      icon: <BrightnessAutoIcon />,
    },
    {
      value: 'schedule',
      label: '定時切換',
      description: '根據時間自動切換主題',
      icon: <ScheduleIcon />,
    },
  ];

  const advancedSettings = [
    {
      icon: <AutoModeIcon />,
      title: '智能調節',
      subtitle: '根據環境光線自動調整',
      key: 'autoAdjust',
      isSwitch: true,
      disabled: themeMode !== 'system',
    },
    {
      icon: <ScheduleIcon />,
      title: '定時提醒',
      subtitle: '主題切換時顯示通知',
      key: 'scheduleNotification',
      isSwitch: true,
      disabled: themeMode !== 'schedule',
    },
  ];

  return (
    <Box sx={{
      bgcolor: 'background.default',
      minHeight: '100vh',
      width: '100%',
      maxWidth: '100vw',
      margin: '0 auto',
    }}>
      {/* 頂部導航欄 */}
      <AppBar
        position="sticky"
        elevation={0}
        sx={{
          bgcolor: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(0,0,0,0.1)',
        }}
      >
        <Toolbar sx={{ maxWidth: '600px', width: '100%', margin: '0 auto' }}>
          <IconButton onClick={handleBack} sx={{ mr: 2 }}>
            <ArrowBackIcon sx={{ color: 'text.primary' }} />
          </IconButton>
          <Typography
            variant="h3"
            sx={{
              flex: 1,
              color: 'text.primary',
              fontWeight: 600,
            }}
          >
            深色模式
          </Typography>
        </Toolbar>
      </AppBar>

      <Box sx={{
        p: 2,
        maxWidth: '600px',
        width: '100%',
        margin: '0 auto',
      }}>
        {/* 當前主題預覽 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card sx={{ 
            borderRadius: 3, 
            mb: 3,
            background: themeMode === 'dark' 
              ? 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)'
              : 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
            color: themeMode === 'dark' ? 'white' : 'text.primary',
            border: `2px solid ${themeMode === 'dark' ? '#444' : '#e0e0e0'}`,
          }}>
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <Box sx={{ mb: 2 }}>
                {themeMode === 'dark' ? (
                  <DarkModeIcon sx={{ fontSize: 48, color: '#bb86fc' }} />
                ) : (
                  <LightModeIcon sx={{ fontSize: 48, color: '#ffa726' }} />
                )}
              </Box>
              <Typography variant="h6" sx={{ mb: 1, fontWeight: 600 }}>
                {themeMode === 'dark' ? '深色模式預覽' : '淺色模式預覽'}
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.8 }}>
                這是當前主題的外觀效果
              </Typography>
              <Chip
                label={`當前：${themeOptions.find(opt => opt.value === themeMode)?.label}`}
                sx={{ 
                  mt: 2,
                  bgcolor: themeMode === 'dark' ? '#bb86fc' : '#6B46C1',
                  color: 'white',
                }}
              />
            </Box>
          </Card>
        </motion.div>

        {/* 主題選擇 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Typography
            variant="h6"
            sx={{
              mb: 1,
              fontWeight: 600,
              color: 'text.secondary',
              fontSize: '0.875rem',
              textTransform: 'uppercase',
              letterSpacing: 0.5,
            }}
          >
            主題選擇
          </Typography>
          <Card sx={{ borderRadius: 3, mb: 3 }}>
            <RadioGroup
              value={themeMode}
              onChange={handleThemeModeChange}
            >
              {themeOptions.map((option, index) => (
                <React.Fragment key={option.value}>
                  <ListItem sx={{ py: 2 }}>
                    <ListItemIcon sx={{ color: 'primary.main', minWidth: 40 }}>
                      {option.icon}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          {option.label}
                        </Typography>
                      }
                      secondary={
                        <Typography variant="body2" color="text.secondary">
                          {option.description}
                        </Typography>
                      }
                    />
                    <FormControlLabel
                      value={option.value}
                      control={<Radio color="primary" />}
                      label=""
                      sx={{ mr: 0 }}
                    />
                  </ListItem>
                  {index < themeOptions.length - 1 && (
                    <Divider variant="inset" component="li" />
                  )}
                </React.Fragment>
              ))}
            </RadioGroup>
          </Card>
        </motion.div>

        {/* 定時設定 */}
        {themeMode === 'schedule' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <Typography
              variant="h6"
              sx={{
                mb: 1,
                fontWeight: 600,
                color: 'text.secondary',
                fontSize: '0.875rem',
                textTransform: 'uppercase',
                letterSpacing: 0.5,
              }}
            >
              定時設定
            </Typography>
            <Card sx={{ borderRadius: 3, mb: 3 }}>
              <List sx={{ py: 0 }}>
                <ListItem sx={{ py: 2 }}>
                  <ListItemIcon sx={{ color: 'primary.main', minWidth: 40 }}>
                    <DarkModeIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="深色模式開始時間"
                    secondary="每天自動切換到深色主題的時間"
                  />
                  <Chip
                    label={settings.startTime}
                    onClick={() => console.log('設定開始時間')}
                    sx={{
                      bgcolor: 'primary.light',
                      color: 'primary.main',
                      cursor: 'pointer',
                    }}
                  />
                </ListItem>
                <Divider variant="inset" component="li" />
                <ListItem sx={{ py: 2 }}>
                  <ListItemIcon sx={{ color: 'primary.main', minWidth: 40 }}>
                    <LightModeIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="淺色模式開始時間"
                    secondary="每天自動切換到淺色主題的時間"
                  />
                  <Chip
                    label={settings.endTime}
                    onClick={() => console.log('設定結束時間')}
                    sx={{
                      bgcolor: 'secondary.light',
                      color: 'secondary.main',
                      cursor: 'pointer',
                    }}
                  />
                </ListItem>
              </List>
            </Card>
          </motion.div>
        )}

        {/* 進階設定 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Typography
            variant="h6"
            sx={{
              mb: 1,
              fontWeight: 600,
              color: 'text.secondary',
              fontSize: '0.875rem',
              textTransform: 'uppercase',
              letterSpacing: 0.5,
            }}
          >
            進階設定
          </Typography>
          <Card sx={{ borderRadius: 3, mb: 3 }}>
            <List sx={{ py: 0 }}>
              {advancedSettings.map((item, index) => (
                <React.Fragment key={index}>
                  <ListItem
                    sx={{
                      py: 2,
                      opacity: item.disabled ? 0.5 : 1,
                    }}
                  >
                    <ListItemIcon
                      sx={{
                        color: 'primary.main',
                        minWidth: 40,
                      }}
                    >
                      {item.icon}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          {item.title}
                        </Typography>
                      }
                      secondary={
                        <Typography variant="body2" color="text.secondary">
                          {item.subtitle}
                        </Typography>
                      }
                    />
                    <Switch
                      checked={settings[item.key] && !item.disabled}
                      onChange={() => !item.disabled && handleSettingChange(item.key)}
                      color="primary"
                      disabled={item.disabled}
                    />
                  </ListItem>
                  {index < advancedSettings.length - 1 && (
                    <Divider variant="inset" component="li" />
                  )}
                </React.Fragment>
              ))}
            </List>
          </Card>
        </motion.div>

        {/* 說明信息 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <Alert severity="info" sx={{ borderRadius: 3 }}>
            <Typography variant="body2">
              💡 深色模式可以減少眼睛疲勞，特別是在光線較暗的環境中使用。
              {themeMode === 'system' && ' 跟隨系統設定可以與您的設備保持一致。'}
              {themeMode === 'schedule' && ' 定時切換可以根據您的作息時間自動調整。'}
            </Typography>
          </Alert>
        </motion.div>
      </Box>
    </Box>
  );
};

export default DarkModeSettingsPage;
